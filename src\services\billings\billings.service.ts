import prisma from "../../prisma";
import { dodoAxios } from "../../lib/axiosDodo";
import paypalClient2 from "../../lib/axiosPaypal2";
import { Provider } from "@prisma/client";

export class BillingsService {
  async getClientsBillingsdetails(
    clientId: string,
    query?: { skip?: number; take?: number }
  ) {
    const paymentsDetails = await prisma.payment.findMany({
      where: {
        userId: clientId,
      },
      select: {
        amount: true,
        paymentId: true,
        paymentMethod: true,
        status: true,
        subscription: {
          select: {
            startDate: true,
            endDate: true,
            next_billing_date: true,
            package: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      skip: query?.skip,
      take: query?.take,
      orderBy: { createdAt: "desc" },
    });

    return paymentsDetails;
  }

  async getCLientsSubscriptionsList(
    clientId: string,
    query?: { skip?: number; take?: number }
  ) {
    const paymentsDetails = await prisma.subscription.findMany({
      where: {
        userId: clientId,
      },
      select: {
        startDate: true,
        endDate: true,
        next_billing_date: true,
        status: true,
        package: {
          select: {
            name: true,
            price: true,
          },
        },
      },
      skip: query?.skip,
      take: query?.take,
      orderBy: { createdAt: "desc" },
    });
    return paymentsDetails;
  }

  async getPaymentsList(query?: { skip?: number; take?: number }) {
    const paymentsDetails = await prisma.payment.findMany({
      select: {
        user: {
          select: {
            name: true,
          },
        },
        createdAt: true,
        amount: true,
        paymentId: true,
        paymentMethod: true,
        status: true,
        subscription: {
          select: {
            startDate: true,
            endDate: true,
            next_billing_date: true,
            package: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      skip: query?.skip,
      take: query?.take,
      orderBy: { createdAt: "desc" },
    });

    return paymentsDetails;
  }

  async downloadInvoice(paymentId: string, clientId?: string) {
    try {
      // First, find the payment and verify access
      const payment = await prisma.payment.findFirst({
        where: {
          paymentId: paymentId,
          ...(clientId && { userId: clientId }), // Only filter by userId if clientId is provided (for client access)
        },
        include: {
          user: {
            select: {
              name: true,
              email: true,
            },
          },
          subscription: {
            include: {
              package: {
                select: {
                  name: true,
                  price: true,
                },
              },
            },
          },
        },
      });

      if (!payment) {
        throw new Error("Payment not found or access denied");
      }

      let invoiceData: Buffer;

      // Fetch invoice based on provider
      console.log(
        `Fetching invoice for payment ${payment.paymentId} from provider ${payment.provider}`
      );

      switch (payment.paymentMethod) {
        case "dodo":
          invoiceData = await this.fetchDodoInvoice(payment.paymentId, payment);
          break;
        case "paypal":
          invoiceData = await this.fetchPayPalInvoice(
            payment.paymentId,
            payment
          );
          break;
        default:
          throw new Error("Unsupported payment provider for invoice download");
      }

      return {
        invoiceData,
        payment,
      };
    } catch (error: any) {
      console.error("Error in downloadInvoice service:", error);
      throw error;
    }
  }

  private async fetchDodoInvoice(
    paymentId: string,
    payment?: any
  ): Promise<Buffer> {
    try {
      // Dodo API call to get invoice PDF
      // Try different possible endpoints for Dodo invoice
      let response;
      try {
        // First try the direct invoice endpoint
        response = await dodoAxios.get(`/invoices/payments/${paymentId}`, {
          responseType: "arraybuffer",
          headers: {
            Accept: "application/pdf",
          },
        });
      } catch (firstError) {
        // If that fails, try alternative endpoints
        try {
          response = await dodoAxios.get(`/invoices/payments/${paymentId}`, {
            responseType: "arraybuffer",
            headers: {
              Accept: "application/pdf",
            },
          });
        } catch (secondError) {
          // Try another common pattern
          response = await dodoAxios.get(`/payments/${paymentId}/receipt`, {
            responseType: "arraybuffer",
            headers: {
              Accept: "application/pdf",
            },
          });
        }
      }

      return Buffer.from(response.data);
    } catch (error: any) {
      console.error("Error fetching Dodo invoice:", error);
      if (error.response) {
        console.error(
          "Dodo API Error:",
          error.response.status,
          error.response.data
        );
      }
      // If all API calls fail, generate a simple text receipt as fallback
      console.log(
        "All Dodo invoice endpoints failed, generating fallback receipt"
      );
      return this.generateFallbackReceipt(payment, "Dodo");
    }
  }

  private async fetchPayPalInvoice(
    paymentId: string,
    payment?: any
  ): Promise<Buffer> {
    try {
      console.log(paymentId, "paymentId");
      // Step 2: Get payment details
      const paymentDetailsRes = await paypalClient2.get(
        `/v2/payments/captures/${paymentId}`
      );

      console.log(paymentDetailsRes, "paymentsDetails");

      const paymentDetails = paymentDetailsRes.data;
      const payerEmail =
        paymentDetails.payer?.email_address ||
        payment?.payer_email ||
        "<EMAIL>";
      const amount = paymentDetails.amount;
      const paymentTime = paymentDetails.create_time;

      // Step 3: Create invoice
      const invoiceCreateRes = await paypalClient2.post(
        `/v2/invoicing/invoices`,
        {
          detail: {
            invoice_number: `INV-${Date.now()}`,
            currency_code: amount.currency_code,
            note: "Thank you for your purchase!",
          },
          invoicer: {
            name: { given_name: "Your Company" },
            email_address: "<EMAIL>",
          },
          primary_recipients: [
            {
              billing_info: {
                name: { given_name: "Customer" },
                email_address: payerEmail,
              },
            },
          ],
          items: [
            {
              name: "Purchase",
              quantity: "1",
              unit_amount: {
                currency_code: amount.currency_code,
                value: amount.value,
              },
            },
          ],
        }
      );

      const invoiceId = invoiceCreateRes.data.id;

      // Step 4: Mark invoice as paid
      await paypalClient2.post(
        `/v2/invoicing/invoices/${invoiceId}/record-payment`,
        {
          payment_date: new Date(paymentTime).toISOString(),
          method: "PAYPAL",
          note: `Paid via PayPal Payment ID: ${paymentId}`,
        }
      );

      // Step 5: Download PDF
      const invoicePdfRes = await paypalClient2.get(
        `/v2/invoicing/invoices/${invoiceId}/pdf`
      );

      return Buffer.from(invoicePdfRes.data);
    } catch (error: any) {
      console.error("Error fetching PayPal invoice:", error);
      if (error.response) {
        console.error(
          "PayPal API Error:",
          error.response.status,
          error.response.data
        );
      }

      // Fallback: Try predefined endpoints (optional)
      try {
        const response = await paypalClient2.get(
          `/v2/invoicing/invoices/${paymentId}/pdf`,
          {
            responseType: "arraybuffer",
            headers: {
              Accept: "application/pdf",
            },
          }
        );
        return Buffer.from(response.data);
      } catch {
        console.log(
          "All PayPal invoice endpoints failed, generating fallback receipt"
        );
        return this.generateFallbackReceipt(payment, "PayPal");
      }
    }
  }

  private generateFallbackReceipt(payment: any, provider: string): Buffer {
    const receiptText = `
PAYMENT RECEIPT
===============

Provider: ${provider}
Payment ID: ${payment.paymentId}
Amount: ${payment.currency} ${payment.amount}
Status: ${payment.status}
Payment Method: ${payment.paymentMethod}
Date: ${new Date(payment.createdAt).toLocaleDateString()}

Customer Information:
Name: ${payment.user?.name || "N/A"}
Email: ${payment.user?.email || "N/A"}

${
  payment.subscription?.package
    ? `
Package Information:
Package: ${payment.subscription.package.name}
Price: ${payment.subscription.package.price}
`
    : ""
}

This is a system-generated receipt.
For any queries, please contact support.

Generated on: ${new Date().toISOString()}
    `;

    return Buffer.from(receiptText, 'utf-8');
  }

  async cancelSubscription(subscriptionId: string, clientId?: string, reason?: string) {
    try {
      // First, find the subscription and verify access
      const subscription = await prisma.subscription.findFirst({
        where: {
          id: subscriptionId,
          ...(clientId && { userId: clientId }), // Only filter by userId if clientId is provided (for client access)
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          package: {
            select: {
              id: true,
              name: true,
              price: true
            }
          }
        }
      });

      if (!subscription) {
        throw new Error("Subscription not found or access denied");
      }

      // Check if subscription is already cancelled
      if (subscription.status === 'CANCELLED') {
        throw new Error("Subscription is already cancelled");
      }

      // Check if subscription can be cancelled
      const nonCancellableStatuses = ['EXPIRED', 'FAILED', 'REFUNDED'];
      if (nonCancellableStatuses.includes(subscription.status)) {
        throw new Error(`Cannot cancel subscription with status: ${subscription.status}`);
      }

      let cancellationResult: any = {};

      // Cancel subscription with external provider based on the subscription data
      if (subscription.paypal_subscription_id) {
        // Cancel PayPal subscription
        cancellationResult = await this.cancelPayPalSubscription(subscription.paypal_subscription_id, reason);
      } else if (subscription.subscription_id) {
        // Cancel Dodo subscription
        cancellationResult = await this.cancelDodoSubscription(subscription.subscription_id, reason);
      }

      // Update subscription status in database
      const updatedSubscription = await prisma.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: 'CANCELLED',
          endDate: new Date(),
          rawResponse: cancellationResult.rawResponse || subscription.rawResponse,
          updatedAt: new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          package: {
            select: {
              id: true,
              name: true,
              price: true
            }
          }
        }
      });

      // Log the cancellation
      console.log(`Subscription ${subscriptionId} cancelled for user ${subscription.userId}. Reason: ${reason || 'Not provided'}`);

      return {
        subscription: updatedSubscription,
        cancellationDetails: cancellationResult,
        message: "Subscription cancelled successfully"
      };

    } catch (error: any) {
      console.error("Error cancelling subscription:", error);
      throw error;
    }
  }

  private async cancelPayPalSubscription(paypalSubscriptionId: string, reason?: string) {
    try {
      console.log(`Cancelling PayPal subscription: ${paypalSubscriptionId}`);
      
      const response = await paypalClient2.post(
        `/v1/billing/subscriptions/${paypalSubscriptionId}/cancel`,
        {
          reason: reason || "User requested cancellation"
        }
      );

      return {
        success: true,
        provider: 'PayPal',
        externalId: paypalSubscriptionId,
        rawResponse: response.data
      };
    } catch (error: any) {
      console.error("Error cancelling PayPal subscription:", error);
      if (error.response) {
        console.error("PayPal API Error:", error.response.status, error.response.data);
      }
      
      // If API call fails, we still want to cancel locally but log the error
      return {
        success: false,
        provider: 'PayPal',
        externalId: paypalSubscriptionId,
        error: error.message,
        rawResponse: error.response?.data
      };
    }
  }

  private async cancelDodoSubscription(dodoSubscriptionId: string, reason?: string) {
    try {
      console.log(`Cancelling Dodo subscription: ${dodoSubscriptionId}`);
      
      const response = await dodoAxios.post(
        `/subscriptions/${dodoSubscriptionId}/cancel`,
        {
          reason: reason || "User requested cancellation"
        }
      );

      return {
        success: true,
        provider: 'Dodo',
        externalId: dodoSubscriptionId,
        rawResponse: response.data
      };
    } catch (error: any) {
      console.error("Error cancelling Dodo subscription:", error);
      if (error.response) {
        console.error("Dodo API Error:", error.response.status, error.response.data);
      }
      
      // If API call fails, we still want to cancel locally but log the error
      return {
        success: false,
        provider: 'Dodo',
        externalId: dodoSubscriptionId,
        error: error.message,
        rawResponse: error.response?.data
      };
    }
  }

  async getSubscriptionDetails(subscriptionId: string, clientId?: string) {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: {
          id: subscriptionId,
          ...(clientId && { userId: clientId }),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          package: {
            select: {
              id: true,
              name: true,
              description: true,
              price: true,
              billingType: true,
              currency: true
            }
          },
          Payment: {
            select: {
              id: true,
              paymentId: true,
              amount: true,
              status: true,
              createdAt: true,
              provider: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 5 // Get last 5 payments
          }
        }
      });

      if (!subscription) {
        throw new Error("Subscription not found or access denied");
      }

      return subscription;
    } catch (error: any) {
      console.error("Error fetching subscription details:", error);
      throw error;
    }
  }
}
