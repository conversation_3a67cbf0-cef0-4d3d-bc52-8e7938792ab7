import { Router } from "express";
import { Client<PERSON>uth<PERSON>ontroller } from "../controllers/client.auth.controller";
import { asyncHandler } from "../middlewares/asyncHandler";
import authMiddleware from "../middlewares/checkAuth";
import { hasRole } from "../middlewares/checkRole";

const router = Router();
const authController = new ClientAuthController();

// Client routes
router.post(
  "/register",
  asyncHandler(authController.register.bind(authController))
);
router.post(
  "/register-otp",
  asyncHandler(authController.verifyRegisterOtp.bind(authController))
);
router.patch(
  "/details",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(authController.updateClientDetails.bind(authController))
);
router.post("/login", asyncHandler(authController.login.bind(authController)));
router.post(
  "/login-otp",
  asyncHandler(authController.verifyLoginOtp.bind(authController))
);
router.post(
  "/logout",
  asyncHandler(authController.logout.bind(authController))
);
// router.post("/refresh-token", asyncHandler(authController.refreshToken.bind(authController)));
router.post(
  "/forgot-password",
  asyncHandler(authController.forgotPassword.bind(authController))
);
router.post(
  "/verify-otp",
  asyncHandler(authController.verifyOtp.bind(authController))
);
router.post(
  "/verify-otp-resend",
  asyncHandler(authController.verifyOtpResend.bind(authController))
);
router.post(
  "/reset-password",
  asyncHandler(authController.resetPassword.bind(authController))
);
router.post(
  "/change-password",
  authMiddleware,
  asyncHandler(authController.changePassword.bind(authController))
);
router.post(
  "/delete-account",
  authMiddleware,
  asyncHandler(authController.deleteAccount.bind(authController))
);
router.post(
  "/send-delete-otp",
  authMiddleware,
  asyncHandler(authController.sendDeleteOtp.bind(authController))
);
router.get(
  "/me",
  authMiddleware,
  asyncHandler(authController.getMe.bind(authController))
);

router.post(
  "/profile-details",
  authMiddleware,
  asyncHandler(authController.saveProfile.bind(authController))
);

router.get(
  "/profile-details",
  authMiddleware,
  asyncHandler(authController.getProfile.bind(authController))
);

router.get(
  "/get-profile-by-id/:id",
  asyncHandler(authController.getUserProfileById.bind(authController))
);

router.get(
  "/getaddress",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(authController.getUserAddress.bind(authController))
);

router.post(
  "/address",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(authController.createOrUpdateUserAddress.bind(authController))
);

router.put(
  "/address",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(authController.createOrUpdateUserAddress.bind(authController))
);

// Admin endpoint to clean up expired OTP users
router.post(
  "/admin/cleanup-expired-otp",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(authController.cleanupExpiredOtpUsers.bind(authController))
);

export default router;
