import { Prisma, PrismaClient, Role } from "@prisma/client";
import { BillingType } from "@prisma/client";
import { AppError } from "../../utils/ApiError";
import { dodoAxios } from "../../lib/axiosDodo";
import paypalClient from "../../lib/axiosPaypal";
import zohoClient from "../../lib/axiosPaypal";
import paypalClient2 from "../../lib/axiosPaypal2";
const prisma = new PrismaClient();

interface PayPalProduct {
  id: string;
  name: string;
  description: string;
}

export class PackageService {
  async getOrCreateZohoProduct(
    name: string,
    description: string
  ): Promise<string> {
    try {
      // Step 1: Try fetching existing products (Zoho doesn't support direct search by name)
      const productListRes = await zohoClient.get("products");
      const products = productListRes.data.products || [];

      const existingProduct = products.find(
        (product: any) => product.name.toLowerCase() === name.toLowerCase()
      );

      if (existingProduct) {
        return existingProduct.product_id;
      }

      // Step 2: Create a new product if not found
      const createRes = await zohoClient.post("products", {
        name,
        description,
        tax_percentage: 0,
      });

      const product = createRes.data.product;
      return product.product_id;
    } catch (error: any) {
      console.error(
        "Zoho Product Error:",
        error?.response?.data || error.message
      );
      throw new AppError("Failed to fetch or create product in Zoho", 500);
    }
  }

  async getOrCreatePaypalProduct(
    name: string,
    description: string
  ): Promise<string> {
    try {
      // Step 1: List existing PayPal products
      const listRes = await paypalClient2.get<{ products: PayPalProduct[] }>(
        "/v1/catalogs/products?page_size=20" // adjust page size or paginate if needed
      );

      const existingProduct = listRes.data.products.find(
        (product) => product.name === name
      );

      if (existingProduct) {
        return existingProduct.id;
      }

      // Step 2: Create product if not found
      const createRes = await paypalClient2.post<PayPalProduct>(
        "/v1/catalogs/products",
        {
          name,
          description,
          type: "SERVICE", // or "PHYSICAL"
          category: "SOFTWARE",
        }
      );

      return createRes.data.id;
    } catch (error: any) {
      console.error(
        "PayPal Product Error:",
        error?.response?.data || error.message
      );
      throw new AppError("Failed to get or create product in PayPal", 500);
    }
  }

async getPackagesCount(isActiveFlag: boolean) {
  const activeCount = await prisma.package.count({
    where: {
      isActive: true,
    },
  });

  if (isActiveFlag && activeCount >= 3) {
    throw new AppError("Only 3 active packages are allowed. Please deactivate one first.", 400);
  }

  return activeCount;
}

  async createPackage(body: {
    name: string;
    description: string;
    price: number;
    billingType: BillingType;
    isActive: boolean;
  }) {
    const { name, description, price, billingType, isActive } = body;

    if (!name || !description || !price || !billingType) {
      throw new AppError("All fields are required", 400);
    }

    // mangaread
    await this.getPackagesCount(isActive); // pass current request's isActive


    // Check if package name already exists
    const existingPackage = await prisma.package.findUnique({
      where: { name },
    });
    if (existingPackage) {
      throw new AppError("Package name already exists", 400);
    }
    // Check if billing type is valid
    const validBillingTypes = Object.values(BillingType);
    if (!validBillingTypes.includes(billingType)) {
      throw new AppError("Invalid billing type", 400);
    }

    const productId = await this.getOrCreateZohoProduct(
      process.env.ZOHO_PRODUCT!,
      description
    );

    const paypalProductId = await this.getOrCreatePaypalProduct(
      process.env.ZOHO_PRODUCT!,
      description
    );

    // ✅ Create plan in Zoho
    const planRes = await zohoClient.post("plans", {
      name,
      description,
      recurring_price: price * 100,
      interval: 1,
      interval_unit: "months",
      setup_fee: 0,
      billing_frequency: 1,
      billing_cycles: 1,
      product_id: productId,
      // product_type: "SAAS",
      plan_code: `${name.toLowerCase().replace(/\s+/g, "_")}_monthly`,
      // is_taxable: false,
    });

    const responseData = await paypalClient2.post("/v1/billing/plans", {
      product_id: paypalProductId,
      name: `${name} Developer Plan`,
      description: `Access to a ${description.toLowerCase()} developer`,
      billing_cycles: [
        {
          frequency: {
            interval_unit: "MONTH",
            interval_count: 1,
          },
          tenure_type: "REGULAR",
          sequence: 1,
          total_cycles: 0,
          pricing_scheme: {
            fixed_price: {
              value: price,
              currency_code: "USD",
            },
          },
        },
      ],
      payment_preferences: {
        auto_bill_outstanding: true,
        setup_fee_failure_action: "CONTINUE",
        payment_failure_threshold: 3,
      },
    });

    console.log(responseData);

    // return responseData.data;

    const response = await dodoAxios.post("products", {
      name,
      price: {
        price: price * 100,
        discount: 0,
        currency: "USD",
        type: "recurring_price",
        payment_frequency_count: 1,
        payment_frequency_interval: "Month",
        subscription_period_count: 1,
        subscription_period_interval: "Month",
        purchasing_power_parity: true,
      },
      tax_category: "saas",
    });

    // console.log(planRes.data, "mangaread");

    const result = await prisma.package.create({
      data: {
        name,
        description,
        price,
        billingType,
        isActive,
        currency: "INR",
        product_id: response.data?.product_id,
        business_id: response.data?.business_id,
        zohoProductId: planRes.data?.plan.plan_id,
        paypalPlanId: responseData.data.id,
      },
    });

    return result;
    // return planRes;
  }

  async getAllPackages(query: { page: number; limit: number; search: string }) {
    const { page, limit, search } = query;
    const skip = (page - 1) * limit;
    const take = limit;
    const where: Prisma.PackageWhereInput | undefined = search
      ? {
          deletedAt: null,
          OR: [
            { name: { contains: search, mode: Prisma.QueryMode.insensitive } },
            {
              description: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
          ],
        }
      : {
          deletedAt: null,
        };
    const packages = await prisma.package.findMany({
      where,
      skip,
      take,
      orderBy: { createdAt: "desc" },
    });
    const totalCount = await prisma.package.count({ where });
    return {
      packages,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
    };
  }

  async getPackageById(id: string) {
    const packageData = await prisma.package.findUnique({
      where: { id },
    });
    if (!packageData) {
      throw new AppError("Package not found", 404);
    }
    return packageData;
  }
  async updatePackage(
    id: string,
    body: {
      name?: string;
      description?: string;
      price?: number;
      billingType?: BillingType;
      isActive?: boolean;
    }
  ) {
    const { name, description, price, billingType, isActive } = body;
    const packageData = await prisma.package.findUnique({
      where: { id },
    });
    if (!packageData) {
      throw new AppError("Package not found", 404);
    }

  if (isActive && !packageData.isActive) {
  await this.getPackagesCount(true); 
}

    // Check if package name already exists
    if (name && name !== packageData.name) {
      const existingPackage = await prisma.package.findUnique({
        where: { name },
      });
      if (existingPackage) {
        throw new AppError("Package name already exists", 400);
      }
    }
    // Check if billing type is valid
    if (billingType && !Object.values(BillingType).includes(billingType)) {
      throw new AppError("Invalid billing type", 400);
    }

    if (packageData.product_id) {
      await dodoAxios.patch(`products/${packageData.product_id}`, {
        name,
        price: {
          price,
          discount: 0,
          currency: "INR",
          type: "recurring_price",
          payment_frequency_count: 1,
          payment_frequency_interval: "Month",
          subscription_period_count: 1,
          subscription_period_interval: "Month",
          purchasing_power_parity: true,
        },
        tax_category: "saas",
      });
    }
    const updatedPackage = await prisma.package.update({
      where: { id },
      data: {
        name: name || packageData.name,
        description: description || packageData.description,
        price: price || packageData.price,
        billingType: billingType || packageData.billingType,
        isActive: isActive !== undefined ? isActive : packageData.isActive,
      },
    });
    return updatedPackage;
  }

  async deletePackage(id: string) {
    const packageData = await prisma.package.findUnique({
      where: { id },
    });
    if (!packageData) {
      throw new AppError("Package not found", 404);
    }
    if (packageData.product_id) {
      await dodoAxios.delete(`products/${packageData.product_id}`);
    }
    await prisma.package.delete({
      where: { id },
    });
    return { message: "Package deleted successfully" };
  }

  async createFeature(body: { rule: string; packageIds: string[] }) {
    const { rule, packageIds } = body;

    // Validate input
    if (
      !rule ||
      !packageIds ||
      !Array.isArray(packageIds) ||
      packageIds.length === 0
    ) {
      throw new AppError("Rule and at least one packageId are required", 400);
    }

    // Check if feature rule already exists
    const existingFeature = await prisma.feature.findFirst({
      where: { rule },
    });
    if (existingFeature) {
      throw new AppError("Feature rule already exists", 400);
    }

    // Verify all packages exist
    const foundPackages = await prisma.package.findMany({
      where: {
        id: { in: packageIds },
      },
    });

    if (foundPackages.length !== packageIds.length) {
      throw new AppError("One or more packageIds are invalid", 404);
    }

    // Create the feature
    const feature = await prisma.feature.create({
      data: { rule },
    });

    // Link feature to packages in the join table (assume available = true by default)
    const packageFeatureData = packageIds.map((packageId) => ({
      featureId: feature.id,
      packageId,
      available: true,
    }));

    await prisma.packageFeature.createMany({
      data: packageFeatureData,
    });

    return { message: "Feature created and linked to packages", feature };
  }

  async getPackageFeatures(packageId: string) {
    const packageData = await prisma.package.findUnique({
      where: { id: packageId },
      include: {
        features: true,
      },
    });
    if (!packageData) {
      throw new AppError("Package not found", 404);
    }
    return packageData.features;
  }
  async updateFeature(
    featureId: string,
    body: {
      rule?: string;
      packageIds: string[]; // new packages to link
    }
  ) {
    const { rule, packageIds } = body;

    // 1. Ensure feature exists
    const feature = await prisma.feature.findUnique({
      where: { id: featureId },
    });
    if (!feature) throw new AppError("Feature not found", 404);

    // 2. Update the feature rule if needed
    const updatedFeature = await prisma.feature.update({
      where: { id: featureId },
      data: {
        rule: rule ?? feature.rule,
      },
    });

    // 3. Remove all existing links for this feature
    await prisma.packageFeature.deleteMany({
      where: { featureId },
    });

    // 4. Re-link to the provided packages
    const linkData = packageIds.map((packageId) => ({
      featureId,
      packageId,
    }));
    await prisma.packageFeature.createMany({ data: linkData });

    return updatedFeature;
  }

  async deleteFeature(featureId: string) {
    const featureData = await prisma.feature.findUnique({
      where: { id: featureId },
    });

    if (!featureData) {
      throw new AppError("Feature not found", 404);
    }

    // Delete all package-feature associations
    await prisma.packageFeature.deleteMany({
      where: { featureId },
    });

    // Delete the feature itself
    await prisma.feature.delete({
      where: { id: featureId },
    });

    return { message: "Feature deleted successfully" };
  }

  async getFeatureById(packageId: string) {
    const result = await prisma.feature.findFirst({
      where: {
        id: packageId,
      },
      include: {
        packages: true,
      },
    });

    if (!result) {
      throw new AppError("Not found", 404);
    }
    return result;
  }
  async getAll(query: { page: number; limit: number; search: string }) {
    const { page, limit, search } = query;
    const skip = (page - 1) * limit;
    const take = limit;

    const where: Prisma.FeatureWhereInput = search
      ? {
          OR: [{ rule: { contains: search, mode: "insensitive" } }],
        }
      : {};

    const features = await prisma.feature.findMany({
      where,
      skip,
      take,
      orderBy: { createdAt: "desc" },
      include: {
        packages: true,
      },
    });

    const totalCount = await prisma.feature.count({ where });

    return {
      features,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
    };
  }

  async getAllActivePackages(query: {
    page: number;
    limit: number;
    search: string;
  }) {
    const { page, limit, search } = query;
    const skip = (page - 1) * limit;
    const take = limit;
    const where: Prisma.PackageWhereInput | undefined = search
      ? {
          deletedAt: null,
          isActive: true,
          OR: [
            { name: { contains: search, mode: Prisma.QueryMode.insensitive } },
            {
              description: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
          ],
        }
      : {
          deletedAt: null,
          isActive: true,
        };
    const packages = await prisma.package.findMany({
      where,
      skip,
      take,
      orderBy: { createdAt: "desc" },
      select: {
        id: true,
        name: true,
      },
    });
    const totalCount = await prisma.package.count({ where });
    return {
      packages,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
    };
  }
}
