{"name": "new-project", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --watch src --ext ts --exec ts-node src/server.ts", "email-cli": "ts-node src/scripts/email-template-cli.ts", "email-preview": "echo 'Email preview available at: http://localhost:3000/api/email-preview'", "email-workflow": "node email-dev-workflow.js", "test-email-system": "node test-email-preview-system.js", "setup-email-templates": "node setup-email-templates.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.10.2", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.1", "@types/swagger-jsdoc": "^6.0.4", "@types/uuid": "^10.0.0", "nodemon": "^3.1.9", "prisma": "^6.10.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@jest/globals": "^29.7.0", "@prisma/adapter-pg": "^6.5.0", "@prisma/client": "^6.10.1", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "commander": "^12.0.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dodopayments": "^1.18.3", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "helmet": "^8.1.0", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1", "pg": "^8.14.1", "prom-client": "^15.1.3", "sib-api-v3-sdk": "^8.5.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^3.24.3"}}