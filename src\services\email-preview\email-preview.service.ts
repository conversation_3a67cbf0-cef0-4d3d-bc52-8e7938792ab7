import { EMAIL_TEMPLATES, Template<PERSON><PERSON><PERSON> } from '../../templates';
import { EmailService } from '../email/email.service';

export interface TemplateInfo {
  name: string;
  displayName: string;
  description: string;
  category: string;
  variables: string[];
  sampleData: Record<string, any>;
}

export class EmailPreviewService {
  private emailService: EmailService;

  constructor() {
    this.emailService = new EmailService();
  }

  /**
   * Get all available email templates with metadata
   */
  getAvailableTemplates(): TemplateInfo[] {
    const templates: TemplateInfo[] = [];

    Object.keys(EMAIL_TEMPLATES).forEach(templateName => {
      const info = this.getTemplateInfo(templateName as keyof typeof EMAIL_TEMPLATES);
      templates.push(info);
    });

    return templates;
  }

  /**
   * Get detailed information about a specific template
   */
  private getTemplateInfo(templateName: keyof typeof EMAIL_TEMPLATES): TemplateInfo {
    const templateContent = EMAIL_TEMPLATES[templateName];
    const variables = this.extractTemplateVariables(templateName);
    const sampleData = this.getSampleDataForTemplate(templateName);

    return {
      name: templateName,
      displayName: this.formatDisplayName(templateName),
      description: this.getTemplateDescription(templateName),
      category: this.getTemplateCategory(templateName),
      variables,
      sampleData
    };
  }

  /**
   * Extract variables from template content
   */
  extractTemplateVariables(templateName: keyof typeof EMAIL_TEMPLATES): string[] {
    const templateContent = EMAIL_TEMPLATES[templateName];
    const variableRegex = /\{\{(\w+(?:\.\w+)?)\}\}/g;
    const variables = new Set<string>();
    
    let match;
    while ((match = variableRegex.exec(templateContent)) !== null) {
      variables.add(match[1]);
    }

    return Array.from(variables).sort();
  }

  /**
   * Get sample data for template testing
   */
  getSampleDataForTemplate(templateName: keyof typeof EMAIL_TEMPLATES): Record<string, any> {
    const baseSampleData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      companyName: 'DataAnnotator Pro',
      supportEmail: '<EMAIL>',
      websiteUrl: 'https://dataannotator.com',
      yourName: 'Sarah Johnson',
      teamName: 'Customer Success Team',
      dashboardUrl: 'https://app.dataannotator.com/dashboard',
      loginUrl: 'https://app.dataannotator.com/login',
      currentDate: new Date().toLocaleDateString(),
      currentYear: new Date().getFullYear().toString()
    };

    // Template-specific sample data
    const templateSpecificData: Record<string, Record<string, any>> = {
      'welcome-mail': {
        meetingLink: 'https://calendly.com/dataannotator/discovery-meeting',
        projectType: 'Image Classification',
        estimatedDuration: '2-3 weeks'
      },
      'otp-verification': {
        otp: '123456',
        expiryTime: '10 minutes'
      },
      'otp-signup': {
        otp: '789012',
        expiryTime: '15 minutes'
      },
      'reset-password': {
        resetLink: 'https://app.dataannotator.com/reset-password?token=abc123',
        expiryTime: '1 hour'
      },
      'password-creation-link': {
        passwordCreationLink: 'https://app.dataannotator.com/create-password?token=xyz789',
        expiryTime: '24 hours'
      },
      'invite-coworker': {
        inviterName: 'Jane Smith',
        inviteLink: 'https://app.dataannotator.com/invite?token=invite123',
        projectName: 'Medical Image Annotation',
        role: 'Senior Annotator'
      },
      'team-assignment': {
        projectName: 'Autonomous Vehicle Dataset',
        teamLead: 'Mike Johnson',
        startDate: '2024-01-15',
        deadline: '2024-02-28',
        teamMembers: ['Alice Brown', 'Bob Wilson', 'Carol Davis']
      },
      'account-suspended': {
        reason: 'Unusual activity detected',
        suspensionDate: new Date().toLocaleDateString(),
        appealLink: 'https://app.dataannotator.com/appeal',
        supportTicketId: 'SUPP-12345'
      },
      'account-reactivated': {
        reactivationDate: new Date().toLocaleDateString(),
        securityTips: [
          'Use a strong, unique password',
          'Enable two-factor authentication',
          'Regularly review account activity'
        ]
      },
      'subscription-expiring': {
        planName: 'Professional Plan',
        expiryDate: '2024-01-31',
        renewalLink: 'https://app.dataannotator.com/billing/renew',
        currentUsage: '85%',
        remainingDays: '7'
      },
      'subscription-expired': {
        planName: 'Professional Plan',
        expiredDate: '2024-01-31',
        renewalLink: 'https://app.dataannotator.com/billing/renew',
        gracePeriod: '7 days'
      },
      'usage-limit-warning': {
        planName: 'Starter Plan',
        currentUsage: '90%',
        usageLimit: '10,000 annotations',
        upgradeLink: 'https://app.dataannotator.com/billing/upgrade',
        resetDate: '2024-02-01'
      },
      'payment-success': {
        amount: '$99.00',
        planName: 'Professional Plan',
        transactionId: 'TXN-*********',
        paymentDate: new Date().toLocaleDateString(),
        nextBillingDate: '2024-02-15',
        invoiceUrl: 'https://app.dataannotator.com/invoices/inv-123'
      },
      'project-completion': {
        projectName: 'Medical Image Classification',
        completionDate: new Date().toLocaleDateString(),
        totalAnnotations: '15,000',
        accuracy: '98.5%',
        downloadLink: 'https://app.dataannotator.com/projects/download/proj-123'
      },
      'system-maintenance': {
        maintenanceDate: '2024-01-20',
        startTime: '2:00 AM UTC',
        endTime: '6:00 AM UTC',
        affectedServices: ['Dashboard', 'API', 'File Upload'],
        statusPageUrl: 'https://status.dataannotator.com'
      },
      'newsletter': {
        newsletterTitle: 'DataAnnotator Monthly Update',
        featuredArticle: 'Best Practices for Image Annotation',
        newFeatures: ['Batch Processing', 'Advanced Analytics', 'Team Collaboration'],
        unsubscribeLink: 'https://app.dataannotator.com/unsubscribe?token=news123'
      },
      'sample-notification': {
        subject: 'Important System Notification',
        title: 'System Update Notification',
        notificationType: 'System',
        notificationTitle: 'Scheduled Maintenance Complete',
        message: 'We have successfully completed the scheduled maintenance on our annotation platform. All services are now fully operational.',
        priority: 'medium',
        actionRequired: true,
        actionUrl: 'https://app.dataannotator.com/dashboard',
        actionText: 'View Dashboard',
        additionalInfo: 'During the maintenance, we improved system performance and added new features to enhance your annotation experience.',
        details: {
          items: [
            'Improved upload speed by 40%',
            'Enhanced annotation tools',
            'Better team collaboration features',
            'Updated security protocols'
          ]
        }
      }
    };

    return {
      ...baseSampleData,
      ...(templateSpecificData[templateName] || {})
    };
  }

  /**
   * Render template with sample or custom data
   */
  renderTemplatePreview(templateName: keyof typeof EMAIL_TEMPLATES, customData?: Record<string, any>): string {
    const sampleData = this.getSampleDataForTemplate(templateName);
    const data = customData ? { ...sampleData, ...customData } : sampleData;
    
    return TemplateRenderer.render(templateName, data);
  }

  /**
   * Send test email
   */
  async sendTestEmail(
    templateName: keyof typeof EMAIL_TEMPLATES,
    email: string,
    customData?: Record<string, any>
  ): Promise<void> {
    const sampleData = this.getSampleDataForTemplate(templateName);
    const data = customData ? { ...sampleData, ...customData } : sampleData;
    
    await this.emailService.sendEmail({
      to: email,
      subject: `[TEST] ${this.formatDisplayName(templateName)} - Email Template Preview`,
      template: templateName,
      data
    });
  }

  /**
   * Generate preview dashboard HTML
   */
  generatePreviewDashboard(): string {
    const templates = this.getAvailableTemplates();
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template Preview Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #2d3748;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #6610f2, #e83e8c);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #6610f2;
        }
        
        .stat-label {
            color: #718096;
            margin-top: 0.5rem;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .template-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            border: 1px solid #e2e8f0;
        }
        
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .template-name {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }
        
        .template-category {
            background: #6610f2;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .template-description {
            color: #718096;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .template-variables {
            margin-bottom: 1rem;
        }
        
        .variables-label {
            font-size: 0.8rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .variables-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }
        
        .variable-tag {
            background: #edf2f7;
            color: #4a5568;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-family: monospace;
        }
        
        .template-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #6610f2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a0fc8;
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .search-bar {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #6610f2;
            box-shadow: 0 0 0 3px rgba(102, 16, 242, 0.1);
        }
        
        .footer {
            text-align: center;
            margin-top: 3rem;
            padding: 2rem;
            color: #718096;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📧 Email Template Preview Dashboard</h1>
        <p>Development and testing interface for email templates</p>
    </div>
    
    <div class="container">
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${templates.length}</div>
                <div class="stat-label">Total Templates</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${new Set(templates.map(t => t.category)).size}</div>
                <div class="stat-label">Categories</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${templates.reduce((sum, t) => sum + t.variables.length, 0)}</div>
                <div class="stat-label">Total Variables</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">✅</div>
                <div class="stat-label">System Status</div>
            </div>
        </div>
        
        <div class="search-bar">
            <input 
                type="text" 
                class="search-input" 
                placeholder="Search templates by name, category, or variables..."
                onkeyup="filterTemplates(this.value)"
            >
        </div>
        
        <div class="templates-grid" id="templatesGrid">
            ${templates.map(template => `
                <div class="template-card" data-template="${template.name}" data-category="${template.category}">
                    <div class="template-header">
                        <div>
                            <div class="template-name">${template.displayName}</div>
                        </div>
                        <div class="template-category">${template.category}</div>
                    </div>
                    
                    <div class="template-description">${template.description}</div>
                    
                    <div class="template-variables">
                        <div class="variables-label">Variables (${template.variables.length}):</div>
                        <div class="variables-list">
                            ${template.variables.slice(0, 8).map(variable => 
                                `<span class="variable-tag">{{${variable}}}</span>`
                            ).join('')}
                            ${template.variables.length > 8 ? `<span class="variable-tag">+${template.variables.length - 8} more</span>` : ''}
                        </div>
                    </div>
                    
                    <div class="template-actions">
                        <a href="/api/email-preview/template/${template.name}" target="_blank" class="btn btn-primary">
                            👁️ Preview
                        </a>
                        <a href="/api/email-preview/template/${template.name}/sample-data" target="_blank" class="btn btn-secondary">
                            📋 Sample Data
                        </a>
                        <a href="/api/email-preview/template/${template.name}/variables" target="_blank" class="btn btn-secondary">
                            🏷️ Variables
                        </a>
                        <button onclick="sendTestEmail('${template.name}')" class="btn btn-success">
                            📤 Test Email
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
    
    <div class="footer">
        <p>Email Template Preview Dashboard - Built for easier development and testing</p>
        <p>Last updated: ${new Date().toLocaleString()}</p>
    </div>
    
    <script>
        function filterTemplates(searchTerm) {
            const cards = document.querySelectorAll('.template-card');
            const term = searchTerm.toLowerCase();
            
            cards.forEach(card => {
                const templateName = card.dataset.template.toLowerCase();
                const category = card.dataset.category.toLowerCase();
                const text = card.textContent.toLowerCase();
                
                if (templateName.includes(term) || category.includes(term) || text.includes(term)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        function sendTestEmail(templateName) {
            const email = prompt('Enter email address for test:');
            if (!email) return;
            
            fetch(\`/api/email-preview/template/\${templateName}/send-test\`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Test email sent successfully!');
                } else {
                    alert('Failed to send test email: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error sending test email: ' + error.message);
            });
        }
    </script>
</body>
</html>
    `;
  }

  /**
   * Format template name for display
   */
  private formatDisplayName(templateName: string): string {
    return templateName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Get template description
   */
  private getTemplateDescription(templateName: string): string {
    const descriptions: Record<string, string> = {
      'welcome-mail': 'Welcome new users and schedule discovery meetings',
      'otp-verification': 'Send OTP codes for account verification',
      'otp-signup': 'Send OTP codes during user registration',
      'reset-password': 'Password reset instructions with secure link',
      'password-creation-link': 'Initial password creation for new accounts',
      'invite-coworker': 'Invite team members to join projects',
      'team-assignment': 'Notify users about project team assignments',
      'account-suspended': 'Inform users about account suspension',
      'account-reactivated': 'Confirm account reactivation',
      'subscription-expiring': 'Warn users about upcoming subscription expiry',
      'subscription-expired': 'Notify users about expired subscriptions',
      'usage-limit-warning': 'Alert users approaching usage limits',
      'payment-success': 'Confirm successful payment transactions',
      'project-completion': 'Notify about completed annotation projects',
      'system-maintenance': 'Inform users about scheduled maintenance',
      'newsletter': 'Regular updates and company news',
      'bank-transfer': 'Bank transfer payment instructions',
      'paypal': 'PayPal payment confirmations',
      'sample-notification': 'General notification template with priority levels'
    };

    return descriptions[templateName] || 'Email template for user communication';
  }

  /**
   * Get template category
   */
  private getTemplateCategory(templateName: string): string {
    const categories: Record<string, string> = {
      'welcome-mail': 'Onboarding',
      'otp-verification': 'Authentication',
      'otp-signup': 'Authentication',
      'reset-password': 'Authentication',
      'password-creation-link': 'Authentication',
      'invite-coworker': 'Team Management',
      'team-assignment': 'Team Management',
      'account-suspended': 'Account Management',
      'account-reactivated': 'Account Management',
      'subscription-expiring': 'Billing',
      'subscription-expired': 'Billing',
      'usage-limit-warning': 'Usage Alerts',
      'payment-success': 'Billing',
      'project-completion': 'Project Management',
      'system-maintenance': 'System Notifications',
      'newsletter': 'Marketing',
      'bank-transfer': 'Billing',
      'paypal': 'Billing',
      'sample-notification': 'System Notifications'
    };

    return categories[templateName] || 'General';
  }
}