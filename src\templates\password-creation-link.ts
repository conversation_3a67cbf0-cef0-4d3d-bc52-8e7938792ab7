export const passwordCreationLinkTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Your Password</title>
</head>
<body style="font-family: 'Helvetica', Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; color: #333333;">
    <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f4f4f4; padding: 20px;">
        <tr>
            <td align="center">
                <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background-color: #171617ff; padding: 20px; text-align: center;">
                            <img src="https://dataannotator01.s3.ap-southeast-2.amazonaws.com/logoannonator/Group.png" alt="Macgence Logo" style="max-width: 140px; height: auto; display: block; margin: 0 auto;">
                        </td>
                    </tr>
                    <!-- Content -->
                    <tr>
                        <td style="padding: 30px;">
                            <p style="font-size: 16px; line-height: 24px; margin: 0 0 20px;">Hi {{firstName}},</p>
                            <p style="font-size: 16px; line-height: 24px; margin: 0 0 20px;">Welcome to <strong style="color: #1a3c34;">{{companyName}}</strong>! To complete your account setup, please create your password by clicking the button below:</p>
                            
                            <!-- CTA Button -->
                            <table width="100%" cellpadding="0" cellspacing="0" style="margin: 30px 0;">
                                <tr>
                                    <td align="center">
                                        <a href="{{passwordCreationLink}}" style="background-color: #1a3c34; color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">🔐 Create Password</a>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Security Info -->
                            <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #e7f3ff; border-left: 4px solid #1a3c34; padding: 15px; border-radius: 4px; margin: 20px 0;">
                                <tr>
                                    <td style="font-size: 14px; line-height: 22px; color: #1a3c34;">
                                        <strong>🔒 Security Note:</strong> This link is valid for <strong>{{expiryTime}}</strong> and can only be used once.
                                    </td>
                                </tr>
                            </table>
                            
                            <p style="font-size: 14px; line-height: 22px; color: #4b5563; margin: 20px 0;">If you're unable to click the button, copy and paste this link into your browser:</p>
                            <p style="font-size: 12px; line-height: 18px; color: #6b7280; word-break: break-all; background-color: #f9fafb; padding: 10px; border-radius: 4px;">{{passwordCreationLink}}</p>
                            
                            <!-- Signature -->
                            <p style="font-size: 14px; line-height: 22px; color: #4b5563; margin: 0;">
                                Best Regards,<br>
                                <span style="color: #1a3c34; font-weight: 600;">The {{companyName}} Team</span>
                            </p>
                        </td>
                    </tr>
                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #171617ff; color: #ffffff; padding: 20px; text-align: center; font-size: 13px; line-height: 20px;">
                            <a href="mailto:{{supportEmail}}" style="color: #f0f0f0ff; text-decoration: none;">{{supportEmail}}</a> | 
                            <a href="{{websiteUrl}}" style="color: #e1e1e1ff; text-decoration: none;">{{websiteUrl}}</a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
`;
