import prisma from "../../prisma";
import { AppError } from "../../utils/ApiError";
import EnhancedEmailService from "../email/enhanced-email.service";

export class AssignmentService {
  private emailService: EnhancedEmailService;

  constructor() {
    this.emailService = new EnhancedEmailService();
  }
  private async createGroupsandUsersConversation(
    coordinatorId: string,
    developerId: string,
    userId: string
  ) {
    // Client-Developer DM
    const existingClientDevDM = await prisma.conversation.findFirst({
      where: {
        isGroup: false,
        participants: {
          every: {
            userId: { in: [userId, developerId] },
          },
          some: {
            userId: userId,
          },
        },
      },
      include: { participants: true },
    });

    let clientDevConversation = existingClientDevDM;
    if (!clientDevConversation) {
      clientDevConversation = await prisma.conversation.create({
        data: {
          isGroup: false,
          participants: {
            create: [{ userId: userId }, { userId: developerId }],
          },
        },
        include: { participants: true },
      });
    }

    // Client-Coordinator DM
    const existingClientCoordDM = await prisma.conversation.findFirst({
      where: {
        isGroup: false,
        participants: {
          every: {
            userId: { in: [userId, coordinatorId] },
          },
          some: {
            userId: userId,
          },
        },
      },
      include: { participants: true },
    });

    let clientCoordConversation = existingClientCoordDM;
    if (!clientCoordConversation) {
      clientCoordConversation = await prisma.conversation.create({
        data: {
          isGroup: false,
          participants: {
            create: [{ userId: userId }, { userId: coordinatorId }],
          },
        },
        include: { participants: true },
      });
    }

    // Developer-Coordinator DM
    const existingDevCoordDM = await prisma.conversation.findFirst({
      where: {
        isGroup: false,
        participants: {
          every: {
            userId: { in: [developerId, coordinatorId] },
          },
          some: {
            userId: developerId,
          },
        },
      },
      include: { participants: true },
    });

    let devCoordConversation = existingDevCoordDM;
    if (!devCoordConversation) {
      devCoordConversation = await prisma.conversation.create({
        data: {
          isGroup: false,
          participants: {
            create: [{ userId: developerId }, { userId: coordinatorId }],
          },
        },
        include: { participants: true },
      });
    }

    // Group chat with all three members
    const potentialGroupChats = await prisma.groupChat.findMany({
      where: {
        isPrivate: true,
        members: {
          some: {
            userId: { in: [userId, developerId, coordinatorId] },
          },
        },
      },
      include: {
        members: true,
      },
    });

    const hasExactMembers = (chatMembers: { userId: string }[]) => {
      const memberIds = chatMembers.map((m) => m.userId).sort();
      const targetIds = [userId, developerId, coordinatorId].sort();
      return JSON.stringify(memberIds) === JSON.stringify(targetIds);
    };

    let groupChat = potentialGroupChats.find((chat) =>
      hasExactMembers(chat.members)
    );
    let groupMessages = null;
    if (!groupChat) {
      const developer = await prisma.user.findUnique({
        where: { id: developerId },
        select: {
          name: true,
          Package: {
            select: {
              name: true,
            },
          },
        },
      });

      const annotatorName = developer?.name || "Annotator";
      const packageName = developer?.Package?.name || "Package";

      const groupName = `${annotatorName} ${packageName} Group`;

      groupMessages = await prisma.groupChat.create({
        data: {
          name: groupName,
          isPrivate: true,
          members: {
            create: [
              { userId: userId },
              { userId: developerId },
              { userId: coordinatorId, role: "ADMIN" },
            ],
          },
        },
      });
    }

    return {
      clientDevConversation,
      clientCoordConversation,
      devCoordConversation,
      groupMessages,
    };
  }
  async createAssignment(data: any) {
    // Implementation for creating an assignment
    return data;
  }

  // async getClientsData(query: any) {
  //   const { page = 1, limit = 10 } = query;

  //   const annotators = await prisma.user.findMany({
  //     where: {
  //       role: {
  //         in: ["CLIENT"],
  //       },
  //       // packageId: {
  //       //   not: null,
  //       // },
  //       Subscription: {
  //         some: {
  //           status: "ACTIVE",
  //         },
  //       },
  //     },

  //     include: {
  //       Subscription: {
  //         where: {
  //           status: "ACTIVE",
  //         },
  //         select: {
  //           id: true,
  //           startDate: true,
  //           endDate: true,
  //           package: {
  //             select: {
  //               name: true,
  //               id: true,
  //             },
  //           },
  //         },
  //       },
  //       assignmentsAsClient: true,
  //     },
  //     orderBy: {
  //       createdAt: "desc",
  //     },
  //     skip: (page - 1) * limit,
  //     take: Number(limit),
  //   });
  //   const totalCount = await prisma.user.count({
  //     where: {
  //       role: {
  //         in: ["CLIENT"],
  //       },
  //       Subscription: {
  //         some: {
  //           status: "ACTIVE",
  //         },
  //       },
  //     },
  //   });
  //   const totalPages = Math.ceil(totalCount / limit);
  //   const hasNextPage = page < totalPages;
  //   const hasPreviousPage = page > 1;
  //   const nextPage = hasNextPage ? page + 1 : null;
  //   const previousPage = hasPreviousPage ? page - 1 : null;

  //   return {
  //     data: annotators,
  //     totalCount,
  //     totalPages,
  //     hasNextPage,
  //     hasPreviousPage,
  //     nextPage,
  //     previousPage,
  //   };
  // }

  async getClientsData(query: any) {
    const { page = 1, limit = 10 } = query;

    const users = await prisma.user.findMany({
      where: {
        role: { in: ["CLIENT"] },
        Subscription: {
          some: {
            status: "ACTIVE",
          },
        },
      },
      include: {
        Subscription: {
          where: { status: "ACTIVE" },
          select: {
            id: true,
            startDate: true,
            endDate: true,
            package: {
              select: {
                name: true,
                id: true,
              },
            },
          },
        },
        assignmentsAsClient: true,
        clientPackageDetails: {
          select: {
            availableFrom: true,
            availableTo: true,
            timezone: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Flatten users with each active subscription and merge clientPackageDetails time info
    const flattenedUsers = users.flatMap((user) => {
      const subscriptions = user.Subscription || [];
      const shifts = user.clientPackageDetails || [];
      const assignments = user.assignmentsAsClient || [];

      const maxLength = Math.max(subscriptions.length, shifts.length);

      return Array.from({ length: maxLength }).map((_, index) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        subscriptionId: subscriptions[index]?.id || null,
        startDate: subscriptions[index]?.startDate || null,
        endDate: subscriptions[index]?.endDate || null,
        packageName: subscriptions[index]?.package?.name || null,
        packageId: subscriptions[index]?.package?.id || null,
        availableFrom: shifts[index]?.availableFrom || null,
        availableTo: shifts[index]?.availableTo || null,
        timezone: shifts[index]?.timezone || null,
        assignmentsAsClient: assignments[index] ? [assignments[index]] : [],
      }));
    });

    const totalCount = flattenedUsers.length;
    const totalPages = Math.ceil(totalCount / limit);
    const paginatedUsers = flattenedUsers.slice(
      (page - 1) * limit,
      page * limit
    );

    return {
      data: paginatedUsers,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async assignTeam({
    userId,
    developerId,
    coordinatorId,
    availableFrom,
    availableTo,
    packageId,
  }: {
    userId: string;
    developerId: string;
    coordinatorId: string;
    availableFrom: string;
    availableTo: string;
    packageId: string;
  }) {
    // Check if already assigned
    const assignment = await prisma.assignment.findFirst({
      where: {
        clientId: userId,
        developerId,
      },
    });
    if (assignment) {
      throw new AppError("Already Assigned", 400);
    }

    // Verify developer exists and is in the same package
    const existingDeveloper = await prisma.user.findUnique({
      where: { id: developerId },
      select: {
        packageId: true,
        name: true,
      },
    });

    const coordinator = await prisma.user.findUnique({
      where: { id: coordinatorId },
      select: {
        name: true,
      },
    });
    if (!existingDeveloper) {
      throw new AppError("Developer not found", 400);
    }
    if (existingDeveloper.packageId !== packageId) {
      throw new AppError("Developer is not in the same package", 400);
    }

    // Get client's shift timing
    const client = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        availableFrom: true,
        availableTo: true,
        email: true,
        name: true,
      },
    });

    if (!client) {
      throw new AppError("Client not found", 400);
    }

    // Create the assignment
    const newAssignment = await prisma.assignment.create({
      data: {
        clientId: userId,
        developerId,
        coordinatorId,
        packageId,
      },
    });

    // Update annotator's shift timing to match the client's timing
    await prisma.user.update({
      where: { id: developerId },
      data: {
        availableFrom: client.availableFrom || availableFrom,
        availableTo: client.availableTo || availableTo,
      },
    });

    await this.createGroupsandUsersConversation(
      coordinatorId,
      developerId,
      userId
    );

    // Send OTP Email using enhanced email service
    await this.emailService.sendEmail({
      to: client.email,
      subject: `Welcome to ${
        process.env.COMPANY_NAME || "Our Platform"
      } - Verify Your Email`,
      template: "team-assignment",
      data: {
        firstName: client.name,
        annotatorName: existingDeveloper.name,
        coordinatorName: coordinator?.name,
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-209-8904",
        websiteUrl: process.env.FRONTEND_URL || "https://getannotator.com/",
      },
    });
    return newAssignment;
  }

  async updateAssignment(id: string, data: any) {
    // Implementation for updating an assignment
    return data;
  }

  async deleteAssignment(id: string) {
    // Implementation for deleting an assignment
    return {};
  }

  async getQuestionariesData(subscriptionId: string) {
    const questionariesData = await prisma.clientPackageDetails.findFirst({
      where: {
        subscriptionId,
      },
    });

    if (!questionariesData) {
      throw new AppError("No Data found related", 404);
    }

    return questionariesData;
  }
}
