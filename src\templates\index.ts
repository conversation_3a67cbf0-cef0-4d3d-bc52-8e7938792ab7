// Email Templates Export
export { resetPasswordTemplate } from './reset_password';
export { bankTransferTemplate } from './bank-transfer';
export { paymentVerifiedTemplate } from './payment-verified';
export { paymentRejectedTemplate } from './payment-rejected';
export { adminBankTransferNotificationTemplate } from './admin-bank-transfer-notification';
export { paypalTemplate } from './paypal';

// New Email Templates
export { otpVerificationTemplate } from './otp-verification';
export { otpSignupTemplate } from './otp-signup';
export { inviteCoworkerTemplate } from './invite-coworker';
export { passwordCreationLinkTemplate } from './password-creation-link';
export { accountsuspendedTemplate } from './account-suspended';
export { accountreactivatedTemplate } from './account-reactivated';
export { welcomemailTemplate } from './welcome-mail';
export { passwordchangedTemplate } from './password-changed';
export { teamassignmentTemplate } from './team-assignment';
export { subscriptionexpiringTemplate } from './subscription-expiring';
export { subscriptionexpiredTemplate } from './subscription-expired';
export { usagelimitwarningTemplate } from './usage-limit-warning';

// Additional Templates
export { paymentSuccessTemplate } from './payment-success';
export { projectCompletionTemplate } from './project-completion';
export { systemMaintenanceTemplate } from './system-maintenance';
export { newsletterTemplate } from './newsletter';
export { sampleNotificationTemplate } from './sample-notification';

// Import all templates for the mapping
import { resetPasswordTemplate } from './reset_password';
import { bankTransferTemplate } from './bank-transfer';
import { paymentVerifiedTemplate } from './payment-verified';
import { paymentRejectedTemplate } from './payment-rejected';
import { adminBankTransferNotificationTemplate } from './admin-bank-transfer-notification';
import { paypalTemplate } from './paypal';
import { otpVerificationTemplate } from './otp-verification';
import { otpSignupTemplate } from './otp-signup';
import { inviteCoworkerTemplate } from './invite-coworker';
import { passwordCreationLinkTemplate } from './password-creation-link';
import { accountsuspendedTemplate } from './account-suspended';
import { accountreactivatedTemplate } from './account-reactivated';
import { welcomemailTemplate } from './welcome-mail';
import { passwordchangedTemplate } from './password-changed';
import { teamassignmentTemplate } from './team-assignment';
import { subscriptionexpiringTemplate } from './subscription-expiring';
import { subscriptionexpiredTemplate } from './subscription-expired';
import { usagelimitwarningTemplate } from './usage-limit-warning';
import { paymentSuccessTemplate } from './payment-success';
import { projectCompletionTemplate } from './project-completion';
import { systemMaintenanceTemplate } from './system-maintenance';
import { newsletterTemplate } from './newsletter';
import { sampleNotificationTemplate } from './sample-notification';

// Template mapping for easy access
export const EMAIL_TEMPLATES: Record<string, string> = {
  'reset-password': resetPasswordTemplate,
  'bank-transfer': bankTransferTemplate,
  'payment-verified': paymentVerifiedTemplate,
  'payment-rejected': paymentRejectedTemplate,
  'admin-bank-transfer-notification': adminBankTransferNotificationTemplate,
  'paypal': paypalTemplate,
  'otp-verification': otpVerificationTemplate,
  'otp-signup': otpSignupTemplate,
  'invite-coworker': inviteCoworkerTemplate,
  'password-creation-link': passwordCreationLinkTemplate,
  'account-suspended': accountsuspendedTemplate,
  'account-reactivated': accountreactivatedTemplate,
  'welcome-mail': welcomemailTemplate,
  'password-changed': passwordchangedTemplate,
  'team-assignment': teamassignmentTemplate,
  'subscription-expiring': subscriptionexpiringTemplate,
  'subscription-expired': subscriptionexpiredTemplate,
  'usage-limit-warning': usagelimitwarningTemplate,
  'payment-success': paymentSuccessTemplate,
  'project-completion': projectCompletionTemplate,
  'system-maintenance': systemMaintenanceTemplate,
  'newsletter': newsletterTemplate,
  'sample-notification': sampleNotificationTemplate,
};

// Template renderer utility
export class TemplateRenderer {
  static render(templateName: keyof typeof EMAIL_TEMPLATES, data: Record<string, any>): string {
    const template = EMAIL_TEMPLATES[templateName];
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }

    let rendered = template;

    // Handle conditional blocks {{#variable}}...{{/variable}}
    rendered = rendered.replace(/\{\{#(\w+)\}\}([\s\S]*?)\{\{\/\1\}\}/g, (match: string, key: string, content: string) => {
      return data[key] ? content : '';
    });

    // Handle nested objects
    rendered = rendered.replace(/\{\{(\w+)\.(\w+)\}\}/g, (match: string, obj: string, key: string) => {
      return data[obj]?.[key] || '';
    });

    // Simple template rendering (replace {{variable}} with data)
    rendered = rendered.replace(/\{\{(\w+)\}\}/g, (match: string, key: string) => {
      return data[key] || '';
    });

    return rendered;
  }
}