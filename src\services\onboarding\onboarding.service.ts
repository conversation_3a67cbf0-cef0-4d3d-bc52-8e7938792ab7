import bcrypt from "bcryptjs";
import prisma from "../../prisma";
import { AnnotatorStatus, Role } from "@prisma/client";
import { AppError } from "../../utils/ApiError";
import { createEmailPasswordVerificationToken } from "../../utils/generate-token";
import { resetPasswordTemplate } from "../../templates/reset_password";
import { mailSender } from "../../utils/mailSender";
import { EnhancedEmailService } from "../email/enhanced-email.service";

class OnboardingService {
  private emailService: EnhancedEmailService;

  constructor() {
    this.emailService = new EnhancedEmailService();
  }
  async createAnnotator({
    name,
    lastname,
    email,
    password,
    role = Role.ANNOTATOR,
    domain,
    packageId,
  }: {
    name: string;
    lastname: string;
    email: string;
    password: string;
    role?: Role;
    domain?: string;
    packageId?: string;
  }) {
    let hashedPassword: string | null = null;
    const existingAnnotator = await prisma.user.findUnique({
      where: {
        email,
      },
    });
    if (existingAnnotator) {
      throw new AppError("Annotator already exists", 400);
    }

    if (packageId) {
      const existingPagekage = await prisma.package.findUnique({
        where: {
          id: packageId,
        },
      });
      if (!existingPagekage) {
        throw new AppError("Package not found", 400);
      }
    }

    console.log(password, "password");
    if (password) {
      hashedPassword = await bcrypt.hash(password, 10);
    } else {
      // Generate a password reset token
      const { resetToken, passwordResetToken, expiresAt } =
        await createEmailPasswordVerificationToken();

      console.log(
        resetToken,
        passwordResetToken,
        expiresAt,
        "resetToken, passwordResetToken, expiresAt"
      );
      await prisma.passwordResetToken.create({
        data: {
          token: passwordResetToken,
          expires: expiresAt,
          identifier: email,
        },
      });

      const link = `${process.env.FRONTEND_URL}reset-password?token=${resetToken}&email=${email}`;
      const subject = "Set your password";
      console.log({
        firstName: name,
        passwordCreationLink: link,
        timeLimit: "1 hour",
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      });
      // Send email with the token using enhanced email service
      await this.emailService.sendEmail({
        to: email,
        subject: `Create Your Password - ${
          process.env.COMPANY_NAME || "Our Platform"
        }`,
        template: "password-creation-link",
        data: {
          firstName: name,
          passwordCreationLink: link,
          timeLimit: "24 hours",
          companyName: process.env.COMPANY_NAME || "Our Platform",
          supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
          websiteUrl: process.env.FRONTEND_URL || "https://company.com",
        },
      });
    }

    const annotator = await prisma.user.create({
      data: {
        name,
        lastname,
        email,
        passwordHash: hashedPassword,
        role,
        packageId: role === "ANNOTATOR" ? packageId : null,
        domain,
        // annotatorStatus: "ACTIVE",
        emailVerified: new Date(),
      },
    });

    if (role === Role.PROJECT_COORDINATOR) {
      await this.createChatsWithAdmins(annotator.id);
    }

    return annotator;
  }

  async getAnnotator(query: any) {
    const { page = 1, limit = 10, role, includeDeleted = false } = query;

    // Build where clause based on role filter
    let whereClause: any = {
      role: {
        in: ["ANNOTATOR", "PROJECT_COORDINATOR"],
      },
    };

    // Exclude soft-deleted users by default
    if (!includeDeleted) {
      whereClause.AND = [
        { isDeleted: { not: true } },
        { accountStatus: { not: "DELETED" } },
      ];
    }

    // If specific role is requested, filter by that role
    if (
      role &&
      ["ANNOTATOR", "PROJECT_COORDINATOR", "CLIENT", "COWORKER"].includes(role)
    ) {
      whereClause.role = role;
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        name: true,
        lastname: true,
        email: true,
        role: true,
        domain: true,
        accountStatus: true,
        packageId: true,
        suspendedUntil: true,
        isDeleted: true,
        createdAt: true,
        updatedAt: true,
        Package: {
          select: {
            name: true,
          },
        },
      },
      skip: (page - 1) * limit,
      take: Number(limit),
    });
    const totalCount = await prisma.user.count({
      where: whereClause,
    });
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;
    const nextPage = hasNextPage ? page + 1 : null;
    const previousPage = hasPreviousPage ? page - 1 : null;

    return {
      data: users,
      totalCount,
      totalPages,
      hasNextPage,
      hasPreviousPage,
      nextPage,
      previousPage,
    };
  }
  async getAnnotatorById(id: string) {
    const annotator = await prisma.user.findUnique({
      where: {
        id,
      },
    });

    if (!annotator) {
      throw new AppError("Annotator not found", 404);
    }

    return annotator;
  }
  async updateAnnotatorStatus(id: string, status: string) {
    const annotator = await prisma.user.findUnique({
      where: {
        id,
      },
    });

    if (!annotator) {
      throw new AppError("Annotator not found", 404);
    }
    if (
      annotator.role !== "ANNOTATOR" &&
      annotator.role !== "PROJECT_COORDINATOR"
    ) {
      throw new AppError("Only annotators can be updated", 400);
    }
    const updatedAnnotator = await prisma.user.update({
      where: { id },
      data: { annotatorStatus: status as AnnotatorStatus },
    });

    return updatedAnnotator;
  }

  async updateAnnotatorAccountStatus(id: string, status: string) {
    const user = await prisma.user.findUnique({
      where: {
        id,
      },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Allow updating account status for all user types that can be managed during onboarding
    if (
      user.role !== "ANNOTATOR" &&
      user.role !== "PROJECT_COORDINATOR" &&
      user.role !== "CLIENT" &&
      user.role !== "COWORKER"
    ) {
      throw new AppError(
        "Cannot update account status for this user type",
        400
      );
    }

    // Validate status
    const validStatuses = ["ACTIVE", "SUSPENDED", "DELETED"];
    if (!validStatuses.includes(status)) {
      throw new AppError("Invalid account status", 400);
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        accountStatus: status as any,
        // Clear suspension date if status is not SUSPENDED
        suspendedUntil: status !== "SUSPENDED" ? null : undefined,
      },
    });

    return updatedUser;
  }
  async deleteAnnotator(id: string) {
    // First, check if the user exists and get their role
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isDeleted: true,
        accountStatus: true,
      },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Check if user is already soft deleted
    if (user.isDeleted || user.accountStatus === "DELETED") {
      throw new AppError("User is already deleted", 400);
    }

    // Only allow deletion of ANNOTATOR and PROJECT_COORDINATOR roles
    if (user.role !== "ANNOTATOR" && user.role !== "PROJECT_COORDINATOR") {
      throw new AppError(
        "Only annotators and coordinators can be deleted",
        400
      );
    }

    // First, check if user is assigned to any clients - if so, prevent deletion entirely
    let clientAssignmentsCount = 0;
    if (user.role === "PROJECT_COORDINATOR") {
      clientAssignmentsCount = await prisma.assignment.count({
        where: { coordinatorId: id },
      });
    } else if (user.role === "ANNOTATOR") {
      clientAssignmentsCount = await prisma.assignment.count({
        where: { developerId: id },
      });
    }

    // If user is assigned to any clients, throw an error and don't allow deletion
    if (clientAssignmentsCount > 0) {
      const clientAssignments = await prisma.assignment.findMany({
        where:
          user.role === "PROJECT_COORDINATOR"
            ? { coordinatorId: id }
            : { developerId: id },
        include: {
          client: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      });

      const clientNames = clientAssignments
        .map((assignment) => assignment.client.name)
        .join(", ");
      throw new AppError(
        `Cannot delete ${user.role.toLowerCase()} as they are currently assigned to ${clientAssignmentsCount} client(s): ${clientNames}. Please unassign them from all clients before deletion.`,
        400
      );
    }

    // Check for other relationships based on user role
    let hasRelationships = false;
    const relationshipDetails: string[] = [];

    if (user.role === "PROJECT_COORDINATOR") {
      // Check coordinator relationships (excluding assignments which we already checked above)
      const [projectsCount, projectAssignmentsCount] = await Promise.all([
        prisma.project.count({
          where: { coordinatorId: id },
        }),
        prisma.projectCoordinatorAssignment.count({
          where: { projectCoordinatorId: id },
        }),
      ]);

      if (projectsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${projectsCount} project(s) as coordinator`);
      }
      if (projectAssignmentsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(
          `${projectAssignmentsCount} project assignment(s)`
        );
      }
    } else if (user.role === "ANNOTATOR") {
      // Check annotator relationships (excluding assignments which we already checked above)
      const [
        annotatorProjectsCount,
        annotatedTasksCount,
        createdTasksCount,
        timeLogsCount,
      ] = await Promise.all([
        prisma.project.count({
          where: {
            annotators: {
              some: { id },
            },
          },
        }),
        prisma.task.count({
          where: {
            annotators: {
              some: { id },
            },
          },
        }),
        prisma.task.count({
          where: { createdById: id },
        }),
        prisma.timeLog.count({
          where: { userId: id },
        }),
      ]);

      if (annotatorProjectsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(
          `${annotatorProjectsCount} project(s) as annotator`
        );
      }
      if (annotatedTasksCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${annotatedTasksCount} annotated task(s)`);
      }
      if (createdTasksCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${createdTasksCount} created task(s)`);
      }
      if (timeLogsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${timeLogsCount} time log(s)`);
      }
    }

    if (hasRelationships) {
      // Perform soft delete if user has relationships
      const softDeletedUser = await prisma.user.update({
        where: { id },
        data: {
          isDeleted: true,
          accountStatus: "DELETED",
          // Optionally, you can also anonymize the email to prevent conflicts
          email: `deleted_${Date.now()}_${user.email}`,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isDeleted: true,
          accountStatus: true,
          createdAt: true,
        },
      });

      return {
        ...softDeletedUser,
        deletionType: "soft",
        message: `User is linked with other entities (${relationshipDetails.join(
          ", "
        )}) and has been soft deleted`,
        relationshipDetails,
      };
    } else {
      // Perform hard delete if no relationships exist
      const deletedUser = await prisma.user.delete({
        where: { id },
      });

      return {
        ...deletedUser,
        deletionType: "hard",
        message:
          "User has been permanently deleted as no relationships were found",
        relationshipDetails: [],
      };
    }
  }
  async updateAnnotator(id: string, data: any) {
    const updatedAnnotator = await prisma.user.update({
      where: { id },
      data,
    });

    return updatedAnnotator;
  }
  async getAnnotatorByEmail(email: string) {
    const annotator = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (!annotator) {
      throw new Error("Annotator not found");
    }

    return annotator;
  }

  async resetPassword(id: string, password: string | null) {
    const annotator = await prisma.user.findUnique({
      where: { id },
    });

    if (!annotator) {
      throw new AppError("Annotator not found", 404);
    }

    if (
      annotator.role !== "ANNOTATOR" &&
      annotator.role !== "PROJECT_COORDINATOR"
    ) {
      throw new AppError("Only annotators can be updated", 400);
    }

    if (!password) {
      const { resetToken, passwordResetToken, expiresAt } =
        await createEmailPasswordVerificationToken();

      await prisma.passwordResetToken.create({
        data: {
          token: passwordResetToken,
          expires: expiresAt,
          identifier: annotator.email,
        },
      });

      const link = `${process.env.FRONTEND_URL}reset-password/${resetToken}`;
      const subject = "Set your password";

      await this.emailService.sendEmail({
        to: annotator.email,
        subject: `Create Your Password - ${
          process.env.COMPANY_NAME || "Our Platform"
        }`,
        template: "password-creation-link",
        data: {
          firstName: annotator.name,
          passwordCreationLink: link,
          timeLimit: "24 hours",
          companyName: process.env.COMPANY_NAME || "Our Platform",
          supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
          websiteUrl: process.env.FRONTEND_URL || "https://company.com",
        },
      });

      return { message: "Password reset email sent successfully." };
    } else {
      const hashedPassword = await bcrypt.hash(password, 10);

      const updatedAnnotator = await prisma.user.update({
        where: { id },
        data: { passwordHash: hashedPassword },
      });

      return updatedAnnotator;
    }
  }

  async suspendUserAccount(id: string, suspendedUntil: number | null = null) {
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Allow suspending all user types except ADMIN
    if (user.role === "ADMIN") {
      throw new AppError("Cannot suspend admin users", 400);
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        accountStatus: "SUSPENDED",
        suspendedUntil: suspendedUntil
          ? new Date(Date.now() + suspendedUntil * 24 * 60 * 60 * 1000)
          : null,
      },
    });

    await this.emailService.sendEmail({
      to: updatedUser.email,
      subject: `Create Your Password - ${
        process.env.COMPANY_NAME || "Our Platform"
      }`,
      template: "account-suspended",
      data: {
        firstName: updatedUser.name,
        platformName: process.env.COMPANY_NAME || "Our Platform",
        suspensionReason: "Nothing",
        suspensionDuration: updatedUser.suspendedUntil,
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return updatedUser;
  }

  async reactivateUserAccount(id: string) {
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Allow reactivating all user types except ADMIN
    if (user.role === "ADMIN") {
      throw new AppError("Cannot modify admin user status", 400);
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        accountStatus: "ACTIVE",
        suspendedUntil: null,
      },
    });

    await this.emailService.sendEmail({
      to: updatedUser.email,
      subject: `Create Your Password - ${
        process.env.COMPANY_NAME || "Our Platform"
      }`,
      template: "account-reactivated",
      data: {
        firstName: updatedUser.name,
        platformName: process.env.COMPANY_NAME || "Our Platform",
        loginLink: process.env.FRONTEND_URL || "https://company.com",
        suspensionDuration: updatedUser.suspendedUntil,
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return updatedUser;
  }

  async activateUserAccount(id: string) {
    const annotator = await prisma.user.findUnique({
      where: { id },
    });

    if (!annotator) {
      throw new AppError("User not found", 404);
    }

    if (
      annotator.role !== "ANNOTATOR" &&
      annotator.role !== "PROJECT_COORDINATOR"
    ) {
      throw new AppError("Only Annotator or Coordinator can be updated", 400);
    }

    const updatedAnnotator = await prisma.user.update({
      where: { id },
      data: {
        accountStatus: "ACTIVE",
        suspendedUntil: null,
      },
    });

    return updatedAnnotator;
  }

  private async createChatsWithAdmins(coordinatorId: string) {
    const admins = await prisma.user.findMany({
      where: {
        role: Role.ADMIN,
      },
      select: {
        id: true,
      },
    });

    const conversationPromises = admins.map((admin) => {
      return prisma.conversation.create({
        data: {
          isGroup: false,
          participants: {
            create: [{ userId: coordinatorId }, { userId: admin.id }],
          },
        },
      });
    });

    await Promise.all(conversationPromises);
  }
}

export const onboardingService = new OnboardingService();
