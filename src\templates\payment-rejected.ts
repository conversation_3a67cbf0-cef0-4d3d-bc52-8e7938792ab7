export const paymentRejectedTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Payment Verification Issue</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
        }
        .warning-banner {
            background-color: #f0fdf4;
            border-left: 4px solid #059669;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .warning-banner-title {
            font-size: 20px;
            font-weight: 600;
            color: #059669;
            padding-bottom: 10px;
        }
        .next-steps {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #1a3c34;
        }
        .next-steps h3 {
            color: #1a3c34;
            margin-top: 0;
            font-size: 16px;
        }
        .next-steps ul {
            margin: 10px 0;
            padding-left: 20px;
            color: #4b5563;
            font-size: 14px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .contact-support {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1a3c34;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .warning-banner-title {
                font-size: 18px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://dataannotator01.s3.ap-southeast-2.amazonaws.com/logoannonator/Group.png" alt="Logo" class="logo">
                </div>

                <div class="content">
                    <div class="warning-banner">
                        <div class="warning-banner-title">⚠️ Payment Verification Issue</div>
                    </div>

                    <p>Dear {{ params.userName }},</p>
                    <p>We have reviewed your bank transfer payment submission, but unfortunately, we were unable to verify the payment at this time.</p>

                    <div class="next-steps">
                        <h3>What You Can Do Next</h3>
                        <ul>
                            <li>Double-check your payment details and transaction ID</li>
                            <li>Ensure the payment screenshot is clear and shows all required information</li>
                            <li>Contact our support team for assistance</li>
                            <li>Resubmit your payment with corrected information if needed</li>
                        </ul>
                    </div>

                    <p>Our support team is here to help you resolve this issue quickly. Please don't hesitate to reach out with any questions or if you need assistance with your payment.</p>

                    <div class="contact-support">
                        <a href="http://app.getannotator.com/contact" class="cta-button">📧 Contact Support</a>
                    </div>

                    <p>We apologize for any inconvenience and appreciate your patience as we work to resolve this matter.</p>

                    <p class="signature">
                        Best Regards,<br>
                        <span>The Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                   <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+13602098904">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;