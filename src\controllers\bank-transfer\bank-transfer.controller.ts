import { Request, Response } from "express";
// import { EnhancedBankTransferService } from "../../services/bank-transfer/bank-transfer-enhanced.service";
import {
  successResponse,
  errorResponse,
  successResponseWithData,
} from "../../helper/apiResponse";
import { AuthenticatedRequest } from "../../middlewares/checkAuth";
import multer from "multer";
import * as S3Service from "../../services/s3/s3.service";
import { BankTransferService } from "../../services/bank-transfer/bank-transfer.service";

export class BankTransferController {
  private bankTransferService: BankTransferService;

  constructor() {
    this.bankTransferService = new BankTransferService();
  }

  /**
   * Submit bank transfer payment details
   */
  async submitBankTransferPayment(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      const {
        packageId,
        amount,
        currency = "USD",
        transactionId,
        bankHolderName,
        accountNumber,
        // ifscCode,
        bankName,
        country,
        city,
        state,
        zipcode,
        street,
        availableFrom,
        availableTo,
        timezone,
        industry,
        category,
        startOn,
        description,
        transactionDate,
        transferedAccNo,
      } = req.body;
  

      console.log ("req.body ", req.body);
      // Validate required fields
      if (
        !packageId ||
        !amount ||
        !transactionId ||
        !bankHolderName ||
        !accountNumber ||
        // !ifscCode ||
        !bankName ||
        !transferedAccNo ||
        !transactionDate
      ) {
        return errorResponse(res, "All payment details are required");
      }

      // Check if screenshot is uploaded
      if (!req.file) {
        return errorResponse(res, "Payment screenshot is required");
      }

      console.log(req.file);

      // Upload screenshot to S3
      const screenshotUrl = await S3Service.default.uploadFile(
        req.file.path,
        "bank-transfer"
      );

      console.log(screenshotUrl);

      const result = await this.bankTransferService.submitBankTransferPayment({
        userId,
        packageId,
        amount: parseFloat(amount),
        currency,
        transactionId,
        bankHolderName,
        accountNumber,
        ifscCode :"",
        bankName,
        screenshotUrl: screenshotUrl ?? req.file.path,
        billing: {
          country,
          city,
          zipcode,
          state,
          street,
        },
        availableFrom,
        availableTo,
        timezone,
        industry,
        category,
        startOn,
        description,
        transactionDate,
        transferedAccNo,
      });

      return successResponse(res, result.message);
    } catch (error: any) {
      console.error("Error submitting bank transfer payment:", error);
      return errorResponse(
        res,
        error.message || "Failed to submit bank transfer payment"
      );
    }
  }

  /**
   * Get pending bank transfer payments (admin only)
   */
  async getPendingBankTransferPayments(
    req: AuthenticatedRequest,
    res: Response
  ) {
    try {
      if (req.user?.userRole !== "ADMIN") {
        return errorResponse(res, "Access denied. Admin access required.");
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result =
        await this.bankTransferService.getPendingBankTransferPayments(
          page,
          limit
        );

      return successResponseWithData(
        res,
        "Pending bank transfer payments retrieved successfully",
        result
      );
    } catch (error: any) {
      console.error("Error getting pending bank transfer payments:", error);
      return errorResponse(
        res,
        error.message || "Failed to get pending bank transfer payments"
      );
    }
  }

  /**
   * Verify bank transfer payment (admin only)
   */
  async verifyBankTransferPayment(req: AuthenticatedRequest, res: Response) {
    try {
      if (req.user?.userRole !== "ADMIN") {
        return errorResponse(res, "Access denied. Admin access required.");
      }

      const { bankTransferPaymentId } = req.params;
      const { status, adminNotes, paymentId } = req.body;
      const verifiedById = req.user?.userId;

      if (!verifiedById) {
        return errorResponse(res, "User authentication required");
      }

      if (!status || !["VERIFIED", "REJECTED"].includes(status)) {
        return errorResponse(
          res,
          "Valid status (VERIFIED or REJECTED) is required"
        );
      }

      const result = await this.bankTransferService.verifyBankTransferPayment({
        bankTransferPaymentId,
        paymentId,
        status,
        adminNotes,
        verifiedById,
      });

      return successResponse(res, result.message);
    } catch (error: any) {
      console.error("Error verifying bank transfer payment:", error);
      return errorResponse(
        res,
        error.message || "Failed to verify bank transfer payment"
      );
    }
  }

  /**
   * Get bank transfer payment details
   */
  async getBankTransferPaymentDetails(
    req: AuthenticatedRequest,
    res: Response
  ) {
    try {
      const { paymentId } = req.params;
      const userId = req.user?.userId;
      const userRole = req.user?.userRole;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      const payment =
        await this.bankTransferService.getBankTransferPaymentDetails(paymentId);

      // Check if user has permission to view this payment
      if (userRole !== "ADMIN" && payment.userId !== userId) {
        return errorResponse(
          res,
          "Access denied. You can only view your own payments."
        );
      }

      return successResponse(
        res,
        "Bank transfer payment details retrieved successfully"
      );
    } catch (error: any) {
      console.error("Error getting bank transfer payment details:", error);
      return errorResponse(
        res,
        error.message || "Failed to get bank transfer payment details"
      );
    }
  }

  /**
   * Get user's bank transfer payments
   */
  async getUserBankTransferPayments(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      const payments =
        await this.bankTransferService.getUserBankTransferPayments(userId);

      return successResponseWithData(
        res,
        "User bank transfer payments retrieved successfully",
        payments
      );
    } catch (error: any) {
      console.error("Error getting user bank transfer payments:", error);
      return errorResponse(
        res,
        error.message || "Failed to get user bank transfer payments"
      );
    }
  }

  /**
   * Get all verified bank transfer users (admin only)
   */
  async getVerifiedBankTransferUsers(req: AuthenticatedRequest, res: Response) {
    try {
      if (req.user?.userRole !== "ADMIN") {
        return errorResponse(res, "Access denied. Admin access required.");
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result =
        await this.bankTransferService.getVerifiedBankTransferUsers(
          page,
          limit
        );

      return successResponseWithData(
        res,
        "Verified bank transfer users retrieved successfully",
        result
      );
    } catch (error: any) {
      console.error("Error getting verified bank transfer users:", error);
      return errorResponse(
        res,
        error.message || "Failed to get verified bank transfer users"
      );
    }
  }

  /**
   * Get bank transfer information (for frontend display)
   */
  async getBankTransferInfo(req: Request, res: Response) {
    try {
      const bankTransferInfo = {
        redirectUrl: "https://skydo.com",
        instructions: [
          "Click the link above to view bank transfer details for your country",
          "Complete the payment using the provided bank details",
          "Fill in the payment information form below",
          "Upload a screenshot of your payment confirmation",
          "Submit the form for admin verification",
          "You will receive an email confirmation once your payment is verified",
        ],
        requiredFields: [
          "Transaction ID",
          "Bank Holder Name",
          "Account Number",
          "IFSC Code",
          "Bank Name",
          "Payment Screenshot",
        ],
        processingTime: "48-72 hours",
        supportEmail: "<EMAIL>",
      };

      return successResponse(
        res,
        "Bank transfer information retrieved successfully"
      );
    } catch (error: any) {
      console.error("Error getting bank transfer info:", error);
      return errorResponse(res, "Failed to get bank transfer information");
    }
  }
}
