export const bankTransferExpirationReminderTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Subscription Expiring Soon</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ff9800;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #ff9800;
            margin-bottom: 10px;
        }
        .warning-icon {
            font-size: 48px;
            color: #ff9800;
            margin-bottom: 20px;
        }
        h1 {
            color: #ff9800;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .expiration-notice {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ff9800;
            text-align: center;
        }
        .days-remaining {
            font-size: 36px;
            font-weight: bold;
            color: #ff9800;
            margin: 10px 0;
        }
        .subscription-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
            word-break: break-word;
        }
        .cta-button {
            display: inline-block;
            background-color: #ff9800;
            color: white !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        .cta-button:hover {
            background-color: #f57c00;
        }
        .renewal-steps {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .renewal-steps h3 {
            color: #4CAF50;
            margin-top: 0;
        }
        .renewal-steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .renewal-steps li {
            margin-bottom: 8px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
            }
            .days-remaining {
                font-size: 28px;
            }
            h1 {
                font-size: 20px;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #2196F3;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <div class="logo">Macgence</div>
                    <div class="warning-icon" aria-hidden="true">⚠️</div>
                    <h1>Subscription Expiring Soon</h1>
                </div>

                <p>Dear {{ params.userName }},</p>

                <p>This is a friendly reminder that your subscription is expiring soon. Please renew to continue enjoying uninterrupted access to our services.</p>

                <div class="expiration-notice {% if params.daysUntilExpiry == 1 %}urgent{% endif %}">
                    <div class="days-remaining">{{ params.daysUntilExpiry }}</div>
                    <p><strong>Day{% if params.daysUntilExpiry > 1 %}s{% endif %} Remaining</strong></p>
                    <p>Your subscription expires on <strong>{{ params.expirationDate }}</strong></p>
                </div>

                <div class="subscription-details">
                    <h3 style="margin-top: 0; color: #2196F3;">Subscription Details</h3>
                    <div class="detail-row">
                        <span class="detail-label">Package:</span>
                        <span class="detail-value">{{ params.packageName }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Expiration Date:</span>
                        <span class="detail-value">{{ params.expirationDate }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Renewal Price:</span>
                        <span class="detail-value">{{ params.packagePrice }}</span>
                    </div>
                </div>

                <div class="renewal-steps">
                    <h3>How to Renew Your Subscription</h3>
                    <ol>
                        <li>Click the "Renew Now" button below.</li>
                        <li>Visit <a href="{{ params.bankTransferLink }}" style="color: #2196F3;">skydo.com</a> for bank transfer details.</li>
                        <li>Complete the bank transfer payment.</li>
                        <li>Submit your payment details and screenshot.</li>
                        <li>Your subscription will be extended once verified.</li>
                    </ol>
                </div>

                <div style="text-align: center;">
                    <a href="{{ params.bankTransferLink }}" class="cta-button">Renew Now - Bank Transfer</a>
                </div>

                <p><strong>Important:</strong> {{ params.renewalInstructions }}</p>

                {% if params.daysUntilExpiry == 1 %}
                <div style="background-color: #ffebee; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f44336;">
                    <p style="margin: 0; color: #f44336; font-weight: bold;">⚠️ URGENT: This is your final reminder! Your subscription expires tomorrow.</p>
                </div>
                {% endif %}

                <p>If you have any questions or need assistance with the renewal process, please don't hesitate to contact our support team.</p>

              

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+***********">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;