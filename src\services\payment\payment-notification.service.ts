import { EmailIntegrationService } from '../email/email-integration.service';

export class PaymentNotificationService {
  private emailService: EmailIntegrationService;

  constructor() {
    this.emailService = new EmailIntegrationService();
  }

  /**
   * Send payment success notification
   */
  async sendPaymentSuccess(data: {
    email: string;
    customerName: string;
    transactionId: string;
    paymentMethod: 'PayPal' | 'Dodo' | 'Bank Transfer' | 'Credit Card';
    amount: string;
    packageName: string;
    dashboardUrl?: string;
  }): Promise<void> {
    await this.emailService.sendPaymentNotification(data.email, {
      customerName: data.customerName,
      paymentStatus: 'success',
      transactionId: data.transactionId,
      paymentMethod: data.paymentMethod,
      amount: data.amount,
      paymentDate: new Date().toLocaleString('en-US', {
        timeZone: 'UTC',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }) + ' UTC',
      packageName: data.packageName,
      dashboardUrl: data.dashboardUrl || process.env.FRONTEND_URL + '/dashboard',
      supportUrl: process.env.FRONTEND_URL + '/support'
    });
  }

  /**
   * Send payment failure notification
   */
  async sendPaymentFailure(data: {
    email: string;
    customerName: string;
    transactionId: string;
    paymentMethod: 'PayPal' | 'Dodo' | 'Bank Transfer' | 'Credit Card';
    amount: string;
    packageName: string;
    failureReason?: string;
  }): Promise<void> {
    const customMessage = data.failureReason 
      ? `We were unable to process your payment due to: ${data.failureReason}. Please review the details below and take the necessary steps to resolve this issue.`
      : undefined;

    await this.emailService.sendPaymentNotification(data.email, {
      customerName: data.customerName,
      paymentStatus: 'failed',
      transactionId: data.transactionId,
      paymentMethod: data.paymentMethod,
      amount: data.amount,
      paymentDate: new Date().toLocaleString('en-US', {
        timeZone: 'UTC',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }) + ' UTC',
      packageName: data.packageName,
      mainMessage: customMessage,
      supportUrl: process.env.FRONTEND_URL + '/support'
    });
  }

  /**
   * Send PayPal payment notification
   */
  async sendPayPalNotification(data: {
    email: string;
    customerName: string;
    transactionId: string;
    amount: string;
    packageName: string;
    status: 'success' | 'failed';
    paypalTransactionId?: string;
  }): Promise<void> {
    if (data.status === 'success') {
      await this.sendPaymentSuccess({
        email: data.email,
        customerName: data.customerName,
        transactionId: data.paypalTransactionId || data.transactionId,
        paymentMethod: 'PayPal',
        amount: data.amount,
        packageName: data.packageName
      });
    } else {
      await this.sendPaymentFailure({
        email: data.email,
        customerName: data.customerName,
        transactionId: data.transactionId,
        paymentMethod: 'PayPal',
        amount: data.amount,
        packageName: data.packageName,
        failureReason: 'PayPal payment was declined or cancelled'
      });
    }
  }

  /**
   * Send Dodo payment notification
   */
  async sendDodoNotification(data: {
    email: string;
    customerName: string;
    transactionId: string;
    amount: string;
    packageName: string;
    status: 'success' | 'failed';
    dodoTransactionId?: string;
  }): Promise<void> {
    if (data.status === 'success') {
      await this.sendPaymentSuccess({
        email: data.email,
        customerName: data.customerName,
        transactionId: data.dodoTransactionId || data.transactionId,
        paymentMethod: 'Dodo',
        amount: data.amount,
        packageName: data.packageName
      });
    } else {
      await this.sendPaymentFailure({
        email: data.email,
        customerName: data.customerName,
        transactionId: data.transactionId,
        paymentMethod: 'Dodo',
        amount: data.amount,
        packageName: data.packageName,
        failureReason: 'Dodo payment processing failed'
      });
    }
  }
}

// Export singleton instance
export const paymentNotificationService = new PaymentNotificationService();
