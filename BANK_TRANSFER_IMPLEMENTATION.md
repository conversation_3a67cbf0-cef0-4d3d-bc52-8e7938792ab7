# Bank Transfer Payment Implementation

## Overview

This implementation provides a complete bank transfer payment mechanism where users can submit bank transfer payment details, upload payment screenshots, and have their payments verified by administrators.

## Features

### User Features
- **Payment Submission**: Users can submit bank transfer payment details including transaction ID, bank details, and payment screenshot
- **Payment Tracking**: Users can view their submitted payments and their verification status
- **Email Notifications**: Users receive email confirmations when payments are submitted, verified, or rejected
- **Skydo Integration**: Frontend redirects to skydo.com for bank transfer details

### Admin Features
- **Payment Review**: Admins can view all pending bank transfer payments
- **Payment Verification**: Admins can approve or reject payments with notes
- **Email Notifications**: <PERSON>mins receive notifications when new payments are submitted
- **Dashboard Integration**: Admin dashboard shows pending payments requiring review

## Database Schema

### BankTransferPayment Model
```prisma
model BankTransferPayment {
  id             String              @id @default(cuid())
  paymentId      String              @unique
  userId         String
  subscriptionId String?
  packageId      String
  amount         Float
  currency       String              @default("USD")
  transactionId  String
  bankHolderName String
  accountNumber  String
  ifscCode       String
  bankName       String
  screenshotUrl  String
  status         BankTransferStatus  @default(PENDING)
  adminNotes     String?
  verifiedAt     DateTime?
  verifiedById   String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt

  user         User          @relation(fields: [userId], references: [id])
  subscription Subscription? @relation(fields: [subscriptionId], references: [id])
  package      Package       @relation(fields: [packageId], references: [id])
  verifiedBy   User?         @relation("BankTransferVerifier", fields: [verifiedById], references: [id])

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

enum BankTransferStatus {
  PENDING
  VERIFIED
  REJECTED
}
```

## API Endpoints

### Public Endpoints

#### GET /api/bank-transfer/info
Get bank transfer information and instructions.

**Response:**
```json
{
  "success": true,
  "message": "Bank transfer information retrieved successfully",
  "data": {
    "redirectUrl": "https://skydo.com",
    "instructions": [...],
    "requiredFields": [...],
    "processingTime": "48-72 hours",
    "supportEmail": "<EMAIL>"
  }
}
```

### User Endpoints (Authenticated)

#### POST /api/bank-transfer/submit
Submit bank transfer payment details with screenshot.

**Request (multipart/form-data):**
- `packageId`: string (required)
- `amount`: number (required)
- `currency`: string (default: "USD")
- `transactionId`: string (required)
- `bankHolderName`: string (required)
- `accountNumber`: string (required)
- `ifscCode`: string (required)
- `bankName`: string (required)
- `screenshot`: file (required, image only, max 5MB)

**Response:**
```json
{
  "success": true,
  "message": "Bank transfer payment details submitted successfully. Your payment is under review.",
  "data": {
    "bankTransferPayment": {...},
    "subscription": {...}
  }
}
```

#### GET /api/bank-transfer/my-payments
Get user's bank transfer payments.

**Response:**
```json
{
  "success": true,
  "message": "User bank transfer payments retrieved successfully",
  "data": [...]
}
```

#### GET /api/bank-transfer/payment/:paymentId
Get specific payment details.

**Response:**
```json
{
  "success": true,
  "message": "Bank transfer payment details retrieved successfully",
  "data": {...}
}
```

### Admin Endpoints (Admin Only)

#### GET /api/bank-transfer/admin/pending
Get pending bank transfer payments for review.

**Query Parameters:**
- `page`: number (default: 1)
- `limit`: number (default: 10)

**Response:**
```json
{
  "success": true,
  "message": "Pending bank transfer payments retrieved successfully",
  "data": {
    "payments": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### POST /api/bank-transfer/admin/verify/:bankTransferPaymentId
Verify (approve/reject) bank transfer payment.

**Request Body:**
```json
{
  "status": "VERIFIED" | "REJECTED",
  "adminNotes": "Optional admin notes"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bank transfer payment verified successfully",
  "data": {
    "bankTransferPayment": {...}
  }
}
```

## Email Templates

### 1. Payment Submission Confirmation
- **Template**: `bank-transfer`
- **Sent to**: User
- **When**: Payment details submitted
- **Purpose**: Confirm submission and provide next steps

### 2. Admin Notification
- **Template**: `admin-bank-transfer-notification`
- **Sent to**: All admin users
- **When**: New payment submitted
- **Purpose**: Notify admins of pending review

### 3. Payment Verified
- **Template**: `payment-verified`
- **Sent to**: User
- **When**: Admin approves payment
- **Purpose**: Confirm payment verification and subscription activation

### 4. Payment Rejected
- **Template**: `payment-rejected`
- **Sent to**: User
- **When**: Admin rejects payment
- **Purpose**: Inform user of rejection with admin notes

## Workflow

### User Workflow
1. **Select Bank Transfer**: User chooses bank transfer as payment method
2. **Redirect to Skydo**: Frontend redirects to skydo.com for bank details
3. **Make Payment**: User completes bank transfer using provided details
4. **Submit Details**: User returns and fills payment form with:
   - Transaction ID
   - Bank holder name
   - Account number
   - IFSC code
   - Bank name
   - Payment screenshot
5. **Confirmation**: User receives email confirmation
6. **Wait for Verification**: Payment status shows as "PENDING"
7. **Notification**: User receives email when payment is verified/rejected

### Admin Workflow
1. **Notification**: Admin receives email about new payment
2. **Review**: Admin logs into dashboard to review payment details
3. **Verification**: Admin checks screenshot and bank records
4. **Decision**: Admin approves or rejects with notes
5. **Activation**: If approved, subscription becomes active
6. **Notification**: User receives verification email

## File Upload

- **Storage**: AWS S3 (configurable)
- **Path**: `bank-transfers/{userId}/{timestamp}-{filename}`
- **Restrictions**: Images only, max 5MB
- **Security**: Authenticated upload, unique file names

## Security Features

- **Authentication**: All user endpoints require valid JWT
- **Authorization**: Admin endpoints restricted to ADMIN role
- **File Validation**: Only image files accepted for screenshots
- **Data Validation**: All required fields validated
- **Unique Constraints**: Payment IDs are unique
- **Audit Trail**: All verification actions logged with admin ID and timestamp

## Testing

Use the provided test file `test-bank-transfer.js` to test the complete flow:

```bash
# Update test configuration
# Set userToken, adminToken, and packageId in testConfig

# Run tests
node test-bank-transfer.js
```

## Frontend Integration

### Payment Form
```javascript
const formData = new FormData();
formData.append('packageId', selectedPackage.id);
formData.append('amount', selectedPackage.price);
formData.append('currency', 'USD');
formData.append('transactionId', userInput.transactionId);
formData.append('bankHolderName', userInput.bankHolderName);
formData.append('accountNumber', userInput.accountNumber);
formData.append('ifscCode', userInput.ifscCode);
formData.append('bankName', userInput.bankName);
formData.append('screenshot', selectedFile);

const response = await fetch('/api/bank-transfer/submit', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`
  },
  body: formData
});
```

### Admin Dashboard
```javascript
// Get pending payments
const pendingPayments = await fetch('/api/bank-transfer/admin/pending', {
  headers: { 'Authorization': `Bearer ${adminToken}` }
});

// Verify payment
const verifyPayment = async (paymentId, status, notes) => {
  await fetch(`/api/bank-transfer/admin/verify/${paymentId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ status, adminNotes: notes })
  });
};
```

## Environment Variables

```env
# S3 Configuration (for file uploads)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=your_region
AWS_S3_BUCKET=your_bucket_name

# Frontend URL (for email links)
FRONTEND_URL=https://your-frontend-domain.com

# Email Configuration
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
```

## Database Migration

Run the migration to add the bank transfer payment table:

```bash
npx prisma db push
# or
npx prisma migrate dev --name add_bank_transfer_payments
```

## Production Considerations

1. **File Storage**: Configure proper S3 bucket with appropriate permissions
2. **Email Service**: Set up reliable SMTP service for notifications
3. **Monitoring**: Add logging and monitoring for payment submissions
4. **Backup**: Regular database backups for payment records
5. **Security**: Implement rate limiting for payment submissions
6. **Compliance**: Ensure PCI compliance for payment data handling

## Support

For issues or questions:
- Email: <EMAIL>
- Documentation: Check API documentation
- Logs: Check application logs for detailed error information