import prisma from "../../prisma";
import { v4 as uuidv4 } from "uuid";
import {
  PaymentStatus,
  SubscriptionStatus,
  Provider,
  BankTransferStatus,
} from "@prisma/client";
import { EmailService } from "../email/email.service";

export interface BankTransferPaymentData {
  userId: string;
  packageId: string;
  amount: number;
  currency: string;
  transactionId: string;
  bankHolderName: string;
  accountNumber: string;
  ifscCode: string;
  bankName: string;
  screenshotUrl: string;
  billing: {
    country: string;
    city: string;
    state: string;
    zipcode: string;
    street: string;
  };
  availableFrom: string;
  availableTo: string;
  timezone: string;
  industry: string;
  category: string;
  startOn?: string;
  description?: string;
  transactionDate: string;
  transferedAccNo: string;
}

export interface BankTransferVerificationData {
  bankTransferPaymentId: string;
  paymentId: string;
  status: BankTransferStatus;
  adminNotes?: string;
  verifiedById: string;
}

export class BankTransferService {
  private emailService: EmailService;

  constructor() {
    this.emailService = new EmailService();
  }

  /**
   * Submit bank transfer payment details
   */
  async submitBankTransferPayment(data: BankTransferPaymentData) {
    try {
      const paymentId = uuidv4();

      // create or update address
      await prisma.billingAddress.upsert({
        where: { userId: data.userId },
        update: {
          country: data.billing?.country,
          address: data.billing?.city,
          state: data.billing?.state,
          postalCodel: data.billing?.zipcode,
          street: data.billing?.street,
        },
        create: {
          userId: data.userId,
          country: data.billing?.country,
          address: data.billing?.city,
          state: data.billing?.state,
          postalCodel: data.billing?.zipcode,
          street: data.billing?.street,
        },
      });

      // Create pending subscription
      const subscription = await prisma.subscription.create({
        data: {
          userId: data.userId,
          packageId: data.packageId,
          status: SubscriptionStatus.PENDING,
          startDate: new Date(),
          endDate: this.calculateEndDate(new Date()),
        },
      });

      await prisma.clientPackageDetails.create({
        data: {
          clientId: data.userId,
          packageId: data.packageId,
          availableFrom: data.availableFrom,
          availableTo: data.availableTo,
          timezone: data.timezone,
          industry: data.industry,
          category: data.category,
          startOn: data.startOn ? new Date(data.startOn) : null,
          description: data.description ?? null,
          subscriptionId: subscription.id,
        },
      });

      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          paymentId,
          userId: data.userId,
          subscriptionId: subscription.id,
          amount: data.amount,
          currency: data.currency,
          paymentMethod: "bank_transfer",
          status: "PENDING",
          provider: "BANK_TRANSFER",
          rawResponse: {
            transactionId: data.transactionId,
            bankHolderName: data.bankHolderName,
            accountNumber: data.accountNumber,
            ifscCode: data.ifscCode,
            bankName: data.bankName,
            screenshotUrl: data.screenshotUrl,
          },
        },
      });

      // Create bank transfer payment record
      const bankTransferPayment = await prisma.bankTransferPayment.create({
        data: {
          paymentId: payment.id,
          userId: data.userId,
          packageId: data.packageId,
          amount: data.amount,
          currency: data.currency,
          transactionId: data.transactionId,
          bankHolderName: data.bankHolderName,
          accountNumber: data.accountNumber,
          ifscCode: data.ifscCode,
          bankName: data.bankName,
          transactionDate: data.transactionDate,
          transferedAccNo: data.transferedAccNo,
          screenshotUrl: data.screenshotUrl,
          status: BankTransferStatus.PENDING,
        },
        include: {
          user: true,
          package: true,
        },
      });

      // Update bank transfer payment with subscription ID
      await prisma.bankTransferPayment.update({
        where: { id: bankTransferPayment.id },
        data: { subscriptionId: subscription.id },
      });

      // Send confirmation email to user
      await this.sendPaymentSubmissionEmail(bankTransferPayment);

      // Send notification to admin
      await this.sendAdminNotificationEmail(bankTransferPayment);

      return {
        success: true,
        bankTransferPayment,
        subscription,
        message:
          "Bank transfer payment details submitted successfully. Your payment is under review.",
      };
    } catch (error) {
      console.error("Error submitting bank transfer payment:", error);
      throw new Error("Failed to submit bank transfer payment");
    }
  }

  /**
   * Get pending bank transfer payments for admin review
   */
  async getPendingBankTransferPayments(page: number = 1, limit: number = 10) {
    try {
      const skip = (page - 1) * limit;

      const [payments, total] = await Promise.all([
        prisma.bankTransferPayment.findMany({
          where: { status: BankTransferStatus.PENDING },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            package: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
            subscription: true,
          },
          orderBy: { createdAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.bankTransferPayment.count({
          where: { status: BankTransferStatus.PENDING },
        }),
      ]);

      return {
        payments,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error("Error getting pending bank transfer payments:", error);
      throw new Error("Failed to get pending bank transfer payments");
    }
  }

  /**
   * Verify bank transfer payment (admin only)
   */
  async verifyBankTransferPayment(data: BankTransferVerificationData) {
    try {
      const bankTransferPayment = await prisma.bankTransferPayment.findUnique({
        where: { id: data.bankTransferPaymentId },
        include: {
          user: true,
          package: true,
          subscription: true,
        },
      });

      console.log(bankTransferPayment, "banck");
      if (!bankTransferPayment) {
        throw new Error("Bank transfer payment not found");
      }

      if (bankTransferPayment.status !== BankTransferStatus.PENDING) {
        throw new Error("Bank transfer payment has already been processed");
      }

      // Update bank transfer payment status
      const updatedPayment = await prisma.bankTransferPayment.update({
        where: { id: data.bankTransferPaymentId },
        data: {
          status: data.status,
          adminNotes: data.adminNotes,
          verifiedAt: new Date(),
          verifiedById: data.verifiedById,
        },
      });

      if (data.status === BankTransferStatus.VERIFIED) {
        // Update subscription to active
        if (bankTransferPayment.subscription) {
          await prisma.subscription.update({
            where: { id: bankTransferPayment.subscription.id },
            data: {
              status: SubscriptionStatus.ACTIVE,
              startDate: new Date(),
            },
          });
        }

        // Update payment status
        await prisma.payment.updateMany({
          where: { id: updatedPayment.paymentId },
          data: {
            status: PaymentStatus.SUCCESS,
            error_message: data.adminNotes || "Payment verification failed",
            updatedAt: new Date(),
          },
        });

        // Send success email to user
        await this.sendPaymentVerifiedEmail(bankTransferPayment);
      } else if (data.status === BankTransferStatus.REJECTED) {
        // Update subscription to failed
        if (bankTransferPayment.subscription) {
          await prisma.subscription.update({
            where: { id: bankTransferPayment.subscription.id },
            data: { status: SubscriptionStatus.FAILED },
          });
        }

        // Update payment status
        await prisma.payment.updateMany({
          where: { id: updatedPayment.paymentId },
          data: {
            status: PaymentStatus.FAILED,
            error_message: data.adminNotes || "Payment verification failed",
            updatedAt: new Date(),
          },
        });

        // Send rejection email to user
        await this.sendPaymentRejectedEmail(updatedPayment);
      }

      return {
        success: true,
        bankTransferPayment: updatedPayment,
        message: `Bank transfer payment ${data.status.toLowerCase()} successfully`,
      };
    } catch (error) {
      console.error("Error verifying bank transfer payment:", error);
      throw new Error("Failed to verify bank transfer payment");
    }
  }

  /**
   * Get bank transfer payment details
   */
  async getBankTransferPaymentDetails(paymentId: string) {
    try {
      const payment = await prisma.bankTransferPayment.findUnique({
        where: { paymentId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          package: true,
          subscription: true,
          verifiedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!payment) {
        throw new Error("Bank transfer payment not found");
      }

      return payment;
    } catch (error) {
      console.error("Error getting bank transfer payment details:", error);
      throw new Error("Failed to get bank transfer payment details");
    }
  }

  /**
   * Get user's bank transfer payments
   */
  async getUserBankTransferPayments(userId: string) {
    try {
      const payments = await prisma.bankTransferPayment.findMany({
        where: { userId },
        include: {
          package: {
            select: {
              id: true,
              name: true,
              price: true,
            },
          },
          subscription: {
            select: {
              id: true,
              status: true,
              startDate: true,
              endDate: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return payments;
    } catch (error) {
      console.error("Error getting user bank transfer payments:", error);
      throw new Error("Failed to get user bank transfer payments");
    }
  }

  /**
   * Get all verified bank transfer users (admin only)
   */
  async getVerifiedBankTransferUsers(page: number = 1, limit: number = 10) {
    try {
      const skip = (page - 1) * limit;

      const [verifiedPayments, total] = await Promise.all([
        prisma.bankTransferPayment.findMany({
          where: {
            status: {
              in: [BankTransferStatus.VERIFIED, BankTransferStatus.REJECTED],
            },
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                lastname: true,
                email: true,
                role: true,
                createdAt: true,
              },
            },
            package: {
              select: {
                id: true,
                name: true,
                price: true,
                billingType: true,
              },
            },
            subscription: {
              select: {
                id: true,
                status: true,
                startDate: true,
                endDate: true,
              },
            },
            verifiedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { verifiedAt: "desc" },
          skip,
          take: limit,
        }),
        prisma.bankTransferPayment.count({
          where: {
            status: {
              in: [BankTransferStatus.VERIFIED, BankTransferStatus.REJECTED],
            },
          },
        }),
      ]);

      // Group by user to avoid duplicates and get user summary
      const userMap = new Map();

      verifiedPayments.forEach((payment) => {
        const userId = payment.user.id;
        if (!userMap.has(userId)) {
          userMap.set(userId, {
            user: payment.user,
            totalPayments: 0,
            totalAmount: 0,
            packages: [],
            subscriptions: [],
            firstPaymentDate: payment.verifiedAt,
            lastPaymentDate: payment.verifiedAt,
            verifiedBy: payment.verifiedBy,
            latestPaymentDetails: {
              transactionId: payment.transactionId,
              bankName: payment.bankName,
              amount: payment.amount,
              currency: payment.currency,
              verifiedAt: payment.verifiedAt,
             
            },
          });
        }

        const userSummary = userMap.get(userId);
        userSummary.totalPayments += 1;
        userSummary.totalAmount += payment.amount;

        // Add unique packages only
        const existingPackage = userSummary.packages.find(
          (pkg: any) => pkg.id === payment.package.id
        );
        if (!existingPackage) {
          userSummary.packages.push(payment.package);
        }

        // Add unique subscriptions only
        const existingSubscription = userSummary.subscriptions.find(
          (sub: any) => sub.id === payment.subscription?.id
        );
        if (!existingSubscription && payment.subscription) {
          userSummary.subscriptions.push(payment.subscription);
        }

        // Update date ranges
        if (
          payment.verifiedAt &&
          payment.verifiedAt < userSummary.firstPaymentDate
        ) {
          userSummary.firstPaymentDate = payment.verifiedAt;
        }
        if (
          payment.verifiedAt &&
          payment.verifiedAt > userSummary.lastPaymentDate
        ) {
          userSummary.lastPaymentDate = payment.verifiedAt;
          // Update latest payment details
          userSummary.latestPaymentDetails = {
            transactionId: payment.transactionId,
            bankName: payment.bankName,
            amount: payment.amount,
            currency: payment.currency,
            verifiedAt: payment.verifiedAt,
          };
        }
      });

      const verifiedUsers = Array.from(userMap.values());

      // Sort users by latest payment date (most recent first)
      verifiedUsers.sort(
        (a, b) =>
          new Date(b.lastPaymentDate).getTime() -
          new Date(a.lastPaymentDate).getTime()
      );

      return {
        verifiedUsers,
        rawPayments: verifiedPayments, // Include raw payment data if needed
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          totalUniqueUsers: userMap.size,
        },
        summary: {
          totalVerifiedPayments: total,
          totalUniqueUsers: userMap.size,
          totalRevenue: verifiedUsers.reduce(
            (sum, user) => sum + user.totalAmount,
            0
          ),
          averagePaymentPerUser:
            userMap.size > 0
              ? verifiedUsers.reduce((sum, user) => sum + user.totalAmount, 0) /
                userMap.size
              : 0,
        },
      };
    } catch (error) {
      console.error("Error getting verified bank transfer users:", error);
      throw new Error("Failed to get verified bank transfer users");
    }
  }

  private calculateEndDate(startDate: Date): Date {
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + 1); // Default to 1 month
    return endDate;
  }

  private async sendPaymentSubmissionEmail(payment: any) {
    try {
      await this.emailService.sendEmail({
        to: payment.user.email,
        subject: "Bank Transfer Payment Submitted - Under Review",
        template: "bank-transfer",
        data: {
          userName: payment.user.name,
          packageName: payment.package.name,
          amount: payment.amount,
          currency: payment.currency,
          transactionId: payment.transactionId,
          paymentId: payment.paymentId,
        },
      });
    } catch (error) {
      console.error("Error sending payment submission email:", error);
    }
  }

  private async sendAdminNotificationEmail(payment: any) {
    try {
      // Get admin users
      const admins = await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { email: true, name: true },
      });

      for (const admin of admins) {
        await this.emailService.sendEmail({
          to: admin.email,
          subject: "New Bank Transfer Payment Submitted for Review",
          template: "admin-bank-transfer-notification",
          data: {
            adminName: admin.name,
            userName: payment.user.name,
            userEmail: payment.user.email,
            packageName: payment.package.name,
            amount: payment.amount,
            currency: payment.currency,
            transactionId: payment.transactionId,
            paymentId: payment.paymentId,
            dashboardLink: `${process.env.FRONTEND_URL}/admin/bank-transfers`,
          },
        });
      }
    } catch (error) {
      console.error("Error sending admin notification email:", error);
    }
  }

  private async sendPaymentVerifiedEmail(payment: any) {
    try {
      await this.emailService.sendEmail({
        to: payment.user.email,
        subject: "Payment Verified - Subscription Activated",
        template: "payment-verified",
        data: {
          userName: payment.user.name,
          packageName: payment.package.name,
          amount: payment.amount,
          currency: payment.currency,
          transactionId: payment.transactionId,
          subscriptionStartDate: new Date().toLocaleDateString(),
          dashboardLink: `${process.env.FRONTEND_URL}/dashboard`,
        },
      });
    } catch (error) {
      console.error("Error sending payment verified email:", error);
    }
  }

  private async sendPaymentRejectedEmail(payment: any) {
    try {
      await this.emailService.sendEmail({
        to: payment.user.email,
        subject: "Payment Verification Failed",
        template: "payment-rejected",
        data: {
          userName: payment.user.name,
          packageName: payment.package.name,
          amount: payment.amount,
          currency: payment.currency,
          transactionId: payment.transactionId,
          adminNotes:
            payment.adminNotes || "Please contact support for more details.",
          supportEmail: "<EMAIL>",
        },
      });
    } catch (error) {
      console.error("Error sending payment rejected email:", error);
    }
  }
}
