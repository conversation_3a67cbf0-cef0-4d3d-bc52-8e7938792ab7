export const subscriptionexpiredTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Subscription Expired</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
            background-color: #ffffff;
        }
        .warning-banner {
            background-color: #f80000ff;
            border-left: 4px solid #f1070fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .warning-banner-title {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            padding-bottom: 10px;
        }
        .features-lost {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #1a3c34;
        }
        .features-lost h3 {
            color: #1a3c34;
            margin-top: 0;
            font-size: 16px;
        }
        .features-lost ul {
            margin: 10px 0;
            padding-left: 20px;
            color: #4b5563;
            font-size: 14px;
        }
        .features-lost li {
            margin-bottom: 8px;
        }
        .contact-support {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 10px;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1a3c34;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .warning-banner-title {
                font-size: 18px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
                margin: 10px 0;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
            }
            .container {
                background-color: #ffffff;
            }
            .content {
                background-color: #ffffff;
                color: #333333;
            }
            .warning-banner {
                background-color: #f91109ff;
                border-left-color: #ffffffff;
            }
            .warning-banner-title {
                color: #ffffffff;
            }
            .features-lost {
                background-color: #e3f2fd;
                border-color: #1a3c34;
            }
            .signature {
                color: #999999;
            }
            .signature span {
                color: #1a3c34;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://dataannotator01.s3.ap-southeast-2.amazonaws.com/logoannonator/Group.png" alt="Logo" class="logo">
                </div>

                <div class="content">
                    <div class="warning-banner">
                        <div class="warning-banner-title">🚨 Subscription Expired</div>
                    </div>

                    <p>Hello {{ params.userName }},</p>
                    <p>Your data is safe and will be restored once you renew your subscription.</p>

                    <div class="features-lost">
                        <h3>⚠️ Features Currently Unavailable:</h3>
                        <ul>
                            <li>Creating new projects</li>
                            <li>Adding new tasks</li>
                            <li>Inviting team members</li>
                            <li>File uploads</li>
                            <li>Advanced features</li>
                        </ul>
                    </div>

                    <div class="contact-support">
                        <a href="http://app.getannotator.com/billing" class="cta-button" style="background-color: #49b903ff;">Renew Now</a>
                        <a href="http://app.getannotator.com/billing" class="cta-button" style="background-color: #0464e2ff;">View Plans</a>
                    </div>

                    <p>Our support team is here to help you get back up and running. Contact us if you have any questions about renewing your subscription or if you'd like to discuss different plan options.</p>

                    <div class="contact-support">
                        <a href="http://app.getannotator.com/contact" class="cta-button" style="background-color: #d90202ff;">📧 Contact Support</a>
                    </div>

                    <p class="signature">
                        Best Regards,<br>
                        <span>The Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                   <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+13602098904">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;
