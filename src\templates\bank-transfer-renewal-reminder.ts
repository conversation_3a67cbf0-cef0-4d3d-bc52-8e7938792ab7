export const bankTransferRenewalReminderTemplate = `

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Subscription Renewal Reminder</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ff9800;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #ff9800;
            margin-bottom: 10px;
        }
        .reminder-icon {
            font-size: 48px;
            color: #ff9800;
            margin-bottom: 20px;
        }
        h1 {
            color: #ff9800;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .urgency-banner {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white !important;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
            font-size: 18px;
        }
        .subscription-details {
            background-color: #fff3e0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ff9800;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
            word-break: break-word;
        }
        .renewal-steps {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .renewal-steps h3 {
            color: #4CAF50;
            margin-top: 0;
        }
        .renewal-steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .renewal-steps li {
            margin-bottom: 8px;
        }
        .cta-button {
            display: inline-block;
            background-color: #ff9800;
            color: white !important;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        .cta-button:hover {
            background-color: #f57c00;
        }
        .countdown {
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            color: #ff9800;
            margin: 20px 0;
        }
        .countdown span {
            font-size: 16px;
            font-weight: normal;
            color: #666;
        }
        .important-note {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #f44336;
        }
        .important-note h4 {
            color: #f44336;
            margin-top: 0;
        }
        .footer {
            margin-top: 30px;
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
            }
            .countdown {
                font-size: 28px;
            }
            h1 {
                font-size: 20px;
            }
            .urgency-banner {
                font-size: 16px;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #2196F3;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <div class="logo">Macgence</div>
                    <div class="reminder-icon" aria-hidden="true">⏰</div>
                    <h1>Subscription Renewal Reminder</h1>
                </div>

                <div class="urgency-banner">
                    Your subscription expires in {{ params.daysRemaining }} day{% if params.daysRemaining > 1 %}s{% endif %}!
                </div>

                <div class="countdown">
                    {{ params.daysRemaining }}
                    <span>day{% if params.daysRemaining > 1 %}s{% endif %} remaining</span>
                </div>

                <p>Dear {{ params.userName }},</p>

                <p>This is a friendly reminder that your <strong>{{ params.packageName }}</strong> subscription will expire soon. To ensure uninterrupted access to your services, please renew your subscription before the expiration date.</p>

                <div class="subscription-details">
                    <h3 style="margin-top: 0; color: #ff9800;">Subscription Details</h3>
                    <div class="detail-row">
                        <span class="detail-label">Package:</span>
                        <span class="detail-value">{{ params.packageName }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Expiration Date:</span>
                        <span class="detail-value">{{ params.expirationDate }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Renewal Amount:</span>
                        <span class="detail-value">{{ params.currency }} {{ params.amount }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Days Remaining:</span>
                        <span class="detail-value" style="color: #ff9800; font-weight: bold;">{{ params.daysRemaining }} day{% if params.daysRemaining > 1 %}s{% endif %}</span>
                    </div>
                </div>

                <div class="renewal-steps">
                    <h3>How to Renew Your Subscription</h3>
                    <ol>
                        {% for instruction in params.renewalInstructions %}
                        <li>{{ instruction }}</li>
                        {% endfor %}
                    </ol>
                </div>

                <div style="text-align: center;">
                    <a href="{{ params.skyDoLink }}" class="cta-button">Get Bank Transfer Details</a>
                </div>

                <div class="important-note">
                    <h4>Important Notice</h4>
                    <p><strong>Automatic Renewal:</strong> If you complete your payment within 7 days after expiration, your subscription will be automatically renewed for another month from the payment verification date.</p>
                    <p><strong>Service Interruption:</strong> If no payment is received, your subscription will expire and access to services will be suspended.</p>
                </div>

                <p>If you have already made the payment, please ensure you've submitted the payment details through our website. It may take up to 48-72 hours for verification.</p>

                <p>Need help? Contact our support team at <a href="mailto:{{ params.supportEmail }}">{{ params.supportEmail }}</a></p>

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+13602098904">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>

`;