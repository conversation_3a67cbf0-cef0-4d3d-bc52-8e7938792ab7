export const adminBankTransferNotificationTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>New Bank Transfer Payment</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
            background-color: #ffffff;
        }
        .notification-banner {
            background-color: #005ff8ff;
            border-left: 4px solid #0037ffff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .notification-banner-title {
            font-size: 20px;
            font-weight: 600;
            color: #0542beff;
            padding-bottom: 10px;
        }
        .notification-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        .user-details {
            background-color: #f3e8ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #312affff;
        }
        .payment-details {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #1a3c34;
        }
        .priority-notice {
            background-color: #fef2f2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc2626;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .detail-label {
            font-weight: bold;
            color: #4b5563;
        }
        .detail-value {
            color: #333333;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 10px;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .notification-banner-title {
                font-size: 18px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
                margin: 10px 0;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
            }
            .container {
                background-color: #ffffff;
            }
            .content {
                background-color: #ffffff;
                color: #333333;
            }
            .notification-banner {
                background-color: #0059f4ff;
                border-left-color: #e9d5ff;
            }
            .notification-banner-title {
                color: #e9d5ff;
            }
            .user-details {
                background-color: #f3e8ff;
                border-left-color: #6b21a8;
            }
            .payment-details {
                background-color: #e3f2fd;
                border-left-color: #1a3c34;
            }
            .priority-notice {
                background-color: #fef2f2;
                border-left-color: #dc2626;
            }
            .detail-label, .detail-value {
                color: #333333;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://dataannotator01.s3.ap-southeast-2.amazonaws.com/logoannonator/Group.png" alt="Logo" class="logo">
                </div>

                <div class="content">
                    <div class="notification-banner">
                        <div class="notification-icon">🔔</div>
                        <div class="notification-banner-title">New Bank Transfer Payment Submitted</div>
                    </div>

                    <p>Dear {{ params.adminName }},</p>

                    <p>A new bank transfer payment has been submitted and requires your review and verification.</p>

                    <div class="user-details">
                        <h3 style="margin-top: 0; color: #0727c3ff;">Customer Information</h3>
                        <div class="detail-row">
                            <span class="detail-label">Customer Name:</span>
                            <span class="detail-value">{{ params.userName }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Customer Email:</span>
                            <span class="detail-value">{{ params.userEmail }}</span>
                        </div>
                    </div>

                    <div class="payment-details">
                        <h3 style="margin-top: 0; color: #1a3c34;">Payment Details</h3>
                        <div class="detail-row">
                            <span class="detail-label">Package:</span>
                            <span class="detail-value">{{ params.packageName }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Amount:</span>
                            <span class="detail-value">{{ params.currency }} {{ params.amount }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Transaction ID:</span>
                            <span class="detail-value">{{ params.transactionId }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Payment ID:</span>
                            <span class="detail-value">{{ params.paymentId }}</span>
                        </div>
                    </div>

                    <div class="priority-notice">
                        <h3 style="margin-top: 0; color: #dc2626;">Action Required</h3>
                        <p>Please review the payment details and uploaded screenshot to verify the transaction. The customer is waiting for confirmation to activate their subscription.</p>
                    </div>

                    <div class="cta-container">
                        <a href="{{ params.dashboardLink }}" class="cta-button">Review Payment in Admin Dashboard</a>
                    </div>

                    <p><strong>Next Steps:</strong></p>
                    <ul>
                        <li>Log into the admin dashboard</li>
                        <li>Review the payment screenshot and details</li>
                        <li>Verify the transaction with bank records</li>
                        <li>Approve or reject the payment</li>
                        <li>Add any necessary notes for the customer</li>
                    </ul>

                    <p>Please process this payment verification as soon as possible to ensure a good customer experience.</p>
                </div>

                <div class="footer">
                    <p><strong>The Team</strong><br>
                        This is an automated notification. Please do not reply to this email.
                    </p>
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+***********">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;