import { Request, Response } from "express";
import crypto from "crypto";
import prisma from "../../prisma";
import { AppError } from "../../utils/ApiError";

export class DodoWebhookController {
  private readonly secret: string;

  constructor() {
    this.secret = process.env.DODO_WEBHOOK_SECRET || "your-default-secret";
  }

  private computeHmac(rawBody: Buffer): string {
    return crypto
      .createHmac("sha256", this.secret)
      .update(rawBody)
      .digest("base64");
  }
  public async handleWebhook(req: Request, res: Response): Promise<void> {
    console.log("Webhook received");
    console.log(req.body, "Request Body");
    console.log(req.headers, "Request Headers");
    console.log(req.body, "Request Body");

    try {
      const signatureHeader = req.headers["webhook-signature"] as string;
      if (!signatureHeader) res.status(400).send("Missing signature header");

      const [version, receivedSignature] = signatureHeader.split(",");

      if (version !== "v1")
        res.status(400).send("Unsupported signature version");

      const rawBody = (req as any).body; // raw body is a Buffer due to express.raw()

      // Example processing
      switch (rawBody?.type) {
        case "payment.succeeded":
          // handle success
          const clietSubscription = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          const reponse2 = await prisma.subscription.update({
            where: { id: clietSubscription?.id },
            data: {
              status: "ACTIVE",
              subscription_id: rawBody?.data?.subscription_id,
            },
          });
          console.log(reponse2, "payment successed");
          if (!clietSubscription?.userId || !clietSubscription?.id) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.upsert({
            where: {
              paymentId: rawBody?.data?.payment_id, // Make sure paymentId is unique in your Prisma schema
            },
            update: {
              userId: clietSubscription?.userId,
              subscriptionId: clietSubscription?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.settlement_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "SUCCESS",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
            create: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscription?.userId,
              subscriptionId: clietSubscription?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.settlement_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "SUCCESS",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });

          break;
        case "payment.failed":
          // handle failure
          const clietSubscriptionFailed = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          await prisma.subscription.update({
            where: { id: clietSubscriptionFailed?.id },
            data: { status: "FAILED" },
          });

          if (
            !clietSubscriptionFailed?.userId ||
            !clietSubscriptionFailed?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          const payment = await prisma.payment.findFirst({
            where: {
              paymentId: rawBody?.data?.payment_id,
            },
          });

          if (payment) {
            await prisma.payment.update({
              where: { id: payment?.id },
              data: { status: "FAILED" },
            });
          }
          if (!payment) {
            await prisma.payment.create({
              data: {
                paymentId: rawBody?.data?.payment_id,
                userId: clietSubscriptionFailed?.userId,
                subscriptionId: clietSubscriptionFailed?.id,
                dodoSubscriptionId: rawBody?.data?.subscription_id,
                amount:
                  rawBody?.data?.total_amount ||
                  rawBody?.data?.recurring_pre_tax_amount ||
                  rawBody?.data?.settlement_amount,
                currency: rawBody?.data?.currency,
                paymentMethod: "dodo",
                status: "FAILED",
                provider: "DODO",
                rawResponse: rawBody,
                businessId: rawBody?.data?.business_id,
                cardLastFour: rawBody?.data?.card_last_four,
                cardNetwork: rawBody?.data?.card_network,
                cardType: rawBody?.data?.card_type,
                cardCountry: rawBody?.data?.card_country,
                taxAmount: rawBody?.data?.tax_amount,
                settlementAmount: rawBody?.data?.settlement_amount,
                settlementCurrency: rawBody?.data?.settlement_currency,
                billingInfo: rawBody?.data?.billing,
                customerEmail: rawBody?.data?.customer_email,
                customerId: rawBody?.data?.customer_id,
                paymentLink: rawBody?.data?.payment_link,
              },
            });
          } else {
            await prisma.payment.update({
              where: { id: payment?.id },
              data: {
                status: "FAILED",
                rawResponse: rawBody,
                error_message: rawBody?.data?.error_message,
              },
            });
          }
          break;
        case "payment.processing":
          // handle processing
          const clietSubscriptionProcessing =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionProcessing?.id },
            data: { status: "PENDING" },
          });

          if (
            !clietSubscriptionProcessing?.userId ||
            !clietSubscriptionProcessing?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionProcessing?.userId,
              subscriptionId: clietSubscriptionProcessing?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "PROCESSING",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "payment.cancelled":
          // handle cancelled
          const clietSubscriptionCancelled =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionCancelled?.id },
            data: { status: "CANCELLED" },
          });

          if (
            !clietSubscriptionCancelled?.userId ||
            !clietSubscriptionCancelled?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionCancelled?.userId,
              subscriptionId: clietSubscriptionCancelled?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "CANCELLED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;

        // Subscription
        case "subscription.active":
          const clietSubscriptionActive = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          if (
            !clietSubscriptionActive?.userId ||
            !clietSubscriptionActive?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          const response1 = await prisma.subscription.update({
            where: { id: clietSubscriptionActive?.id },
            data: {
              status: "ACTIVE",
              next_billing_date: new Date(rawBody.data.next_billing_date),
              previous_billing_date: new Date(
                rawBody.data.previous_billing_date
              ),
              subscription_id: rawBody.data.subscription_id,
              product_id: rawBody.data.product_id,
              recurring_amount: rawBody.data.recurring_pre_tax_amount,
              subscription_period_interval:
                rawBody.data.subscription_period_interval,
              payment_frequency_interval:
                rawBody.data.payment_frequency_interval,
              subscription_period_count: rawBody.data.subscription_period_count,
              payment_frequency_count: rawBody.data.payment_frequency_count,
              updatedAt: new Date(),
              rawResponse: rawBody,
            },
          });

          console.log(response1, "successed");

          break;
        case "subscription.on_hold":
          const clietSubscriptionHold = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          if (!clietSubscriptionHold?.userId || !clietSubscriptionHold?.id) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.subscription.update({
            where: { id: clietSubscriptionHold?.id },
            data: {
              status: "PAUSED",
              updatedAt: new Date(),
              rawResponse: rawBody,
            },
          });
          break;
        case "subscription.renewed":
          const clietSubscriptionRenewed = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          if (
            !clietSubscriptionRenewed?.userId ||
            !clietSubscriptionRenewed?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          const response = await prisma.subscription.update({
            where: { id: clietSubscriptionRenewed?.id },
            data: {
              status: rawBody.data.status === "active" ? "ACTIVE" : "PAUSED",
              next_billing_date: new Date(rawBody.data.next_billing_date),
              updatedAt: new Date(),
              rawResponse: rawBody,
            },
          });
          console.log(response, "renewed");
          break;
        case "subscription.paused":
          const clietSubscriptionOnHold = await prisma.subscription.findFirst({
            where: {
              subscription_id: rawBody?.data?.subscription_id,
            },
          });

          await prisma.subscription.update({
            where: { id: clietSubscriptionOnHold?.id },
            data: {
              status: "PAUSED",
              updatedAt: new Date(),
              rawResponse: rawBody,
            },
          });

          break;
        case "subscription.cancelled":
          const clietSubscriptionCancelled1 =
            await prisma.subscription.findFirst({
              where: {
                subscription_id: rawBody?.data?.subscription_id,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionCancelled1?.id },
            data: { status: "CANCELLED" },
          });

          break;
        case "subscription.failed":
          const data = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          if (!data?.userId || !data?.id) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.subscription.updateMany({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
            data: {
              status: "FAILED",
              updatedAt: new Date(),
              rawResponse: rawBody,
            },
          });
          break;
        case "subscription.expired":
          break;

        // Refund
        case "refund.succeeded":
          // handle refunded
          const existingRefund = await prisma.refund.findUnique({
            where: { refundId: rawBody?.data?.refund_id },
          });

          if (!existingRefund) {
            await prisma.refund.create({
              data: {
                refundId: rawBody?.data?.refund_id,
                paymentId: rawBody?.data?.payment_id,
                businessId: rawBody?.data?.business_id,
                amount: rawBody?.data?.amount,
                currency: rawBody?.data?.currency,
                reason: rawBody?.data?.reason,
                status: rawBody?.data?.status,
                createdAt: new Date(rawBody?.data?.created_at),
              },
            });
          }

          break;
        case "refund.failed":
          // handle refunded
          const existingRefundFailed = await prisma.refund.findUnique({
            where: { refundId: rawBody?.data?.refund_id },
          });

          if (!existingRefundFailed) {
            await prisma.refund.create({
              data: {
                refundId: rawBody?.data?.refund_id,
                paymentId: rawBody?.data?.payment_id,
                businessId: rawBody?.data?.business_id,
                amount: rawBody?.data?.amount,
                currency: rawBody?.data?.currency,
                reason: rawBody?.data?.reason,
                status: rawBody?.data?.status,
                createdAt: new Date(rawBody?.data?.created_at),
              },
            });
          }
          break;

        // Dispute
        case "dispute.opened":
          // handle dispute created
          const clietSubscriptionDispute = await prisma.subscription.findFirst({
            where: {
              externalReference: rawBody?.data?.metadata?.externalReference,
            },
          });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDispute?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDispute?.userId ||
            !clietSubscriptionDispute?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDispute?.userId,
              subscriptionId: clietSubscriptionDispute?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "dispute.expired":
          // handle dispute expired
          const clietSubscriptionDisputeExpired =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDisputeExpired?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDisputeExpired?.userId ||
            !clietSubscriptionDisputeExpired?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDisputeExpired?.userId,
              subscriptionId: clietSubscriptionDisputeExpired?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "dispute.accepeted":
          // handle dispute won
          const clietSubscriptionDisputeAccepted =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDisputeAccepted?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDisputeAccepted?.userId ||
            !clietSubscriptionDisputeAccepted?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDisputeAccepted?.userId,
              subscriptionId: clietSubscriptionDisputeAccepted?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "dispute.cancelled":
          // handle dispute cancelled
          const clietSubscriptionDisputeCancelled =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDisputeCancelled?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDisputeCancelled?.userId ||
            !clietSubscriptionDisputeCancelled?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDisputeCancelled?.userId,
              subscriptionId: clietSubscriptionDisputeCancelled?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "dispute.challenged":
          // handle dispute challenged
          const clietSubscriptionDisputeChallenged =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDisputeChallenged?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDisputeChallenged?.userId ||
            !clietSubscriptionDisputeChallenged?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDisputeChallenged?.userId,
              subscriptionId: clietSubscriptionDisputeChallenged?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "dispute.won":
          // handle dispute won
          const clietSubscriptionDisputeWon =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDisputeWon?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDisputeWon?.userId ||
            !clietSubscriptionDisputeWon?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDisputeWon?.userId,
              subscriptionId: clietSubscriptionDisputeWon?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        case "dispute.lost":
          // handle dispute lost
          const clietSubscriptionDisputeLost =
            await prisma.subscription.findFirst({
              where: {
                externalReference: rawBody?.data?.metadata?.externalReference,
              },
            });

          await prisma.subscription.update({
            where: { id: clietSubscriptionDisputeLost?.id },
            data: { status: "DISPUTED" },
          });

          if (
            !clietSubscriptionDisputeLost?.userId ||
            !clietSubscriptionDisputeLost?.id
          ) {
            throw new AppError("Missing userId or subscriptionId", 400);
          }

          await prisma.payment.create({
            data: {
              paymentId: rawBody?.data?.payment_id,
              userId: clietSubscriptionDisputeLost?.userId,
              subscriptionId: clietSubscriptionDisputeLost?.id,
              dodoSubscriptionId: rawBody?.data?.subscription_id,
              amount: rawBody?.data?.recurring_pre_tax_amount,
              currency: rawBody?.data?.currency,
              paymentMethod: "dodo",
              status: "DISPUTED",
              provider: "DODO",
              rawResponse: rawBody,
              businessId: rawBody?.data?.business_id,
              cardLastFour: rawBody?.data?.card_last_four,
              cardNetwork: rawBody?.data?.card_network,
              cardType: rawBody?.data?.card_type,
              cardCountry: rawBody?.data?.card_country,
              taxAmount: rawBody?.data?.tax_amount,
              settlementAmount: rawBody?.data?.settlement_amount,
              settlementCurrency: rawBody?.data?.settlement_currency,
              billingInfo: rawBody?.data?.billing,
              customerEmail: rawBody?.data?.customer_email,
              customerId: rawBody?.data?.customer_id,
              paymentLink: rawBody?.data?.payment_link,
            },
          });
          break;
        default:
          console.warn("Unhandled event type:", event?.type);
      }

      res.status(200).json({ message: "Received" });
    } catch (error) {
      console.error("Webhook error:", error);
      res.status(400).json({ error: "Invalid request" });
    }
  }
}
