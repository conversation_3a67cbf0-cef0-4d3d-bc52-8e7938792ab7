import { EnhancedEmailService } from './enhanced-email.service';
import { UserCommunicationService } from './user-communication.service';

/**
 * Centralized email integration service that provides easy access to all email functionality
 * This service acts as a facade for all email-related operations
 */
export class EmailIntegrationService {
  private enhancedEmailService: EnhancedEmailService;
  private userCommunicationService: UserCommunicationService;

  constructor() {
    this.enhancedEmailService = new EnhancedEmailService();
    this.userCommunicationService = new UserCommunicationService();
  }

  // ===== AUTHENTICATION & ONBOARDING EMAILS =====

  /**
   * Send OTP for signup verification
   */
  async sendSignupOTP(to: string, data: {
    firstName: string;
    otpCode: string;
    timeLimit?: number;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `Welcome to ${process.env.COMPANY_NAME || 'Our Platform'} - Verify Your Email`,
      template: 'otp-signup',
      data: {
        ...data,
        timeLimit: data.timeLimit || 10,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        phoneNumber: process.env.SUPPORT_PHONE || '******-567-8900',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send OTP for verification (password reset, account deletion, etc.)
   */
  async sendVerificationOTP(to: string, data: {
    customerName: string;
    otpCode: string;
    purpose?: string;
    timeLimit?: number;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `${data.purpose || 'Verification'} - OTP Code`,
      template: 'otp-verification',
      data: {
        ...data,
        timeLimit: data.timeLimit || 10,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        phoneNumber: process.env.SUPPORT_PHONE || '******-567-8900',
        website: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send password creation link
   */
  async sendPasswordCreationLink(to: string, data: {
    firstName: string;
    passwordCreationLink: string;
    timeLimit?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `Create Your Password - ${process.env.COMPANY_NAME || 'Our Platform'}`,
      template: 'password-creation-link',
      data: {
        ...data,
        timeLimit: data.timeLimit || "1 hour",
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  // ===== ACCOUNT MANAGEMENT EMAILS =====

  /**
   * Send account suspended notification
   */
  async sendAccountSuspended(to: string, data: {
    firstName: string;
    suspensionReason: string;
    suspensionDate?: string;
    suspensionDuration?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `Account Suspended - ${process.env.COMPANY_NAME || 'Our Platform'}`,
      template: 'account-suspended',
      data: {
        ...data,
        platformName: process.env.COMPANY_NAME || 'Our Platform',
        suspensionDate: data.suspensionDate || new Date().toLocaleDateString(),
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        supportPhone: process.env.SUPPORT_PHONE || '******-567-8900',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send account reactivated notification
   */
  async sendAccountReactivated(to: string, data: {
    firstName: string;
    loginLink?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Account Reactivated - Welcome Back!',
      template: 'account-reactivated',
      data: {
        ...data,
        platformName: process.env.COMPANY_NAME || 'Our Platform',
        loginLink: data.loginLink || process.env.FRONTEND_URL || 'https://company.com',
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send password changed confirmation
   */
  async sendPasswordChanged(to: string, data: {
    firstName: string;
    dateTime?: string;
    email: string;
    ipAddress?: string;
    resetLink?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `Password Changed Successfully - ${process.env.COMPANY_NAME || 'Our Platform'}`,
      template: 'password-changed',
      data: {
        ...data,
        platformName: process.env.COMPANY_NAME || 'Our Platform',
        dateTime: data.dateTime || new Date().toLocaleString(),
        ipAddress: data.ipAddress || 'Unknown',
        resetLink: data.resetLink || `${process.env.FRONTEND_URL}/forgot-password`,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  // ===== COLLABORATION EMAILS =====

  /**
   * Send coworker invitation
   */
  async sendCoworkerInvitation(to: string, data: {
    coworkerName: string;
    clientName: string;
    role: string;
    invitationLink: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `You're invited to join ${data.clientName} on ${process.env.COMPANY_NAME || 'Our Platform'}`,
      template: 'invite-coworker',
      data: {
        ...data,
        platformName: process.env.COMPANY_NAME || 'Our Platform',
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(to: string, data: {
    firstName: string;
    meetingLink?: string;
    yourName?: string;
    teamName?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `Welcome to ${process.env.COMPANY_NAME || 'Our Platform'} - Let's Get Started`,
      template: 'welcome-mail',
      data: {
        ...data,
        meetingLink: data.meetingLink || `${process.env.FRONTEND_URL}/onboarding`,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send team assignment notification
   */
  async sendTeamAssignment(to: string, data: {
    firstName: string;
    projectName: string;
    projectId: string;
    startDate: string;
    annotatorName: string;
    annotatorInitials: string;
    annotatorExpertise: string;
    annotatorEmail: string;
    annotatorPhone?: string;
    coordinatorName: string;
    coordinatorInitials: string;
    coordinatorEmail: string;
    coordinatorPhone?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Your Team is Ready - Annotator and Coordinator Assigned',
      template: 'team-assignment',
      data: {
        ...data,
        platformName: process.env.COMPANY_NAME || 'Our Platform',
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  // ===== SUBSCRIPTION & BILLING EMAILS =====

  /**
   * Send subscription expiring warning
   */
  async sendSubscriptionExpiring(to: string, data: {
    firstName: string;
    daysUntilExpiry: number;
    packageName: string;
    endDate: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Subscription Expiring Soon',
      template: 'subscription-expiring',
      data: {
        ...data,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
        dashboardUrl: `${process.env.FRONTEND_URL}/dashboard`,
      }
    });
  }

  /**
   * Send subscription expired notification
   */
  async sendSubscriptionExpired(to: string, data: {
    firstName: string;
    packageName: string;
    endDate: string;
    expiredDays: number;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Subscription Expired',
      template: 'subscription-expired',
      data: {
        ...data,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
        dashboardUrl: `${process.env.FRONTEND_URL}/dashboard`,
      }
    });
  }

  /**
   * Send usage limit warning
   */
  async sendUsageLimitWarning(to: string, data: {
    firstName: string;
    resource: string;
    current: number;
    limit: number;
    percentage: number;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Usage Limit Warning',
      template: 'usage-limit-warning',
      data: {
        ...data,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
        dashboardUrl: `${process.env.FRONTEND_URL}/dashboard`,
      }
    });
  }

  // ===== ADDITIONAL TEMPLATES =====

  /**
   * Send payment success notification
   */
  async sendPaymentSuccess(to: string, data: {
    firstName: string;
    transactionId: string;
    packageName: string;
    paymentMethod: string;
    paymentDate: string;
    currency: string;
    amount: string;
    dashboardUrl?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Payment Successful - Thank You!',
      template: 'payment-success',
      data: {
        ...data,
        dashboardUrl: data.dashboardUrl || `${process.env.FRONTEND_URL}/dashboard`,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send project completion notification
   */
  async sendProjectCompletion(to: string, data: {
    firstName: string;
    projectName: string;
    startDate: string;
    completionDate: string;
    projectDuration: string;
    totalTasks: number;
    totalHours: number;
    qualityScore: number;
    annotatorName: string;
    annotatorEmail: string;
    coordinatorName: string;
    coordinatorEmail: string;
    projectUrl?: string;
    downloadUrl?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: `Project "${data.projectName}" Completed Successfully!`,
      template: 'project-completion',
      data: {
        ...data,
        projectUrl: data.projectUrl || `${process.env.FRONTEND_URL}/projects`,
        downloadUrl: data.downloadUrl || `${process.env.FRONTEND_URL}/downloads`,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send system maintenance notification
   */
  async sendSystemMaintenance(to: string, data: {
    firstName: string;
    startTime: string;
    endTime: string;
    duration: string;
    timezone: string;
    services?: Array<{ serviceName: string; impact: string }>;
    impactDescription: string;
    preparationSteps?: Array<{ step: string }>;
    statusPageUrl?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: 'Scheduled System Maintenance Notification',
      template: 'system-maintenance',
      data: {
        ...data,
        statusPageUrl: data.statusPageUrl || `${process.env.FRONTEND_URL}/status`,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
      }
    });
  }

  /**
   * Send newsletter
   */
  async sendNewsletter(to: string, data: {
    firstName: string;
    newsletterTitle: string;
    newsletterDate: string;
    introMessage: string;
    sections?: Array<{
      sectionTitle: string;
      articles: Array<{
        title: string;
        excerpt: string;
        url: string;
      }>;
    }>;
    featureHighlight?: {
      title: string;
      description: string;
      url: string;
      buttonText: string;
    };
    stats?: {
      items: Array<{
        number: string;
        label: string;
      }>;
    };
    socialLinks?: Array<{
      name: string;
      url: string;
    }>;
    companyAddress?: string;
    unsubscribeUrl?: string;
    preferencesUrl?: string;
  }): Promise<void> {
    await this.enhancedEmailService.sendEmail({
      to,
      subject: data.newsletterTitle,
      template: 'newsletter',
      data: {
        ...data,
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
        unsubscribeUrl: data.unsubscribeUrl || `${process.env.FRONTEND_URL}/unsubscribe`,
        preferencesUrl: data.preferencesUrl || `${process.env.FRONTEND_URL}/preferences`,
      }
    });
  }

  // ===== LEGACY SUPPORT =====

  /**
   * Send email using legacy mailSender format (for backward compatibility)
   */
  async sendLegacyEmail(user: { name: string; email: string; subject: string; body: string }): Promise<boolean> {
    return await this.enhancedEmailService.sendLegacyEmail(user);
  }

  // ===== UTILITY METHODS =====

  /**
   * Test email service connection
   */
  async testConnection(): Promise<boolean> {
    return await this.enhancedEmailService.testConnection();
  }

  /**
   * Send bulk emails
   */
  async sendBulkEmails(emails: Array<{
    to: string | string[];
    subject: string;
    template: string;
    data: any;
  }>): Promise<void> {
    await this.enhancedEmailService.sendBulkEmails(emails);
  }
}

export default EmailIntegrationService;