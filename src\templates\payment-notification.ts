export const paymentNotificationTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Payment {{paymentStatus}} - {{companyName}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
        }
        .status-banner {
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
            border-left: 4px solid;
        }
        .status-banner.success {
            background-color: #f0fdf4;
            border-left-color: #059669;
        }
        .status-banner.failed {
            background-color: #fef2f2;
            border-left-color: #dc2626;
        }
        .status-banner-title {
            font-size: 20px;
            font-weight: 600;
            padding-bottom: 10px;
        }
        .status-banner.success .status-banner-title {
            color: #059669;
        }
        .status-banner.failed .status-banner-title {
            color: #dc2626;
        }
        .payment-details {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e2e8f0;
        }
        .payment-details h3 {
            color: #1a3c34;
            margin-top: 0;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #4b5563;
        }
        .detail-value {
            color: #1f2937;
        }
        .next-steps {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #1a3c34;
        }
        .next-steps h3 {
            color: #1a3c34;
            margin-top: 0;
            font-size: 16px;
        }
        .next-steps ul {
            margin: 10px 0;
            padding-left: 20px;
            color: #4b5563;
            font-size: 14px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 5px;
        }
        .cta-button.secondary {
            background-color: #6b7280;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1a3c34;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
            border-radius: 0 0 8px 8px;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .status-banner-title {
                font-size: 18px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
                margin: 5px 0;
            }
            .detail-row {
                flex-direction: column;
                gap: 5px;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="{{logoUrl}}" alt="{{companyName}} Logo" class="logo">
                </div>

                <div class="content">
                    <div class="status-banner {{paymentStatus}}">
                        <div class="status-banner-title">{{statusIcon}} Payment {{paymentStatusTitle}}!</div>
                        <p>{{statusMessage}}</p>
                    </div>

                    <p>Dear {{customerName}},</p>
                    <p>{{mainMessage}}</p>

                    <div class="payment-details">
                        <h3>💳 Payment Details</h3>
                        <div class="detail-row">
                            <span class="detail-label">Transaction ID:</span>
                            <span class="detail-value">{{transactionId}}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Payment Method:</span>
                            <span class="detail-value">{{paymentMethod}}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Amount:</span>
                            <span class="detail-value">{{amount}}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Date & Time:</span>
                            <span class="detail-value">{{paymentDate}}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Package:</span>
                            <span class="detail-value">{{packageName}}</span>
                        </div>
                    </div>

                    <div class="next-steps">
                        <h3>{{nextStepsTitle}}</h3>
                        <ul>
                            {{nextStepsList}}
                        </ul>
                    </div>

                    <div class="cta-container">
                        {{ctaButtons}}
                    </div>

                    <p>{{closingMessage}}</p>

                    <p class="signature">
                        Best Regards,<br>
                        <span>The {{companyName}} Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> |
                    <a href="https://getannotator.com">https://getannotator.com</a>
                    {{phoneNumberSection}}
                    <br>
                    <span>Need help? Reach us via our <a href="{{contactUrl}}">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;
