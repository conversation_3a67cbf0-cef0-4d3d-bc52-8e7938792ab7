import { Request, Response } from "express";
import { ClientAuthService } from "../services/auth.service";
import { mailSender } from "../utils/mailSender";
import prisma from "../prisma";
import { generateOtp } from "../utils/otp";
import { AppError } from "../utils/ApiError";
import { AuthenticatedRequest } from "../middlewares/checkAuth";
import {
  errorResponse,
  successResponse,
  successResponseWithData,
} from "../helper/apiResponse";
import EnhancedEmailService from "../services/email/enhanced-email.service";

// interface AuthenticatedRequest extends Request {
//   user?: JwtPayload;
// }
const authService = new ClientAuthService();

export class ClientAuthController {
   private emailService: EnhancedEmailService;
  
    constructor() {
      this.emailService = new EnhancedEmailService();
    }
  // Register Controller
  // async register(req: Request, res: Response) {
  //   const { name, email, password, role } = req.body;

  //   // Create a new user
  //   const newUser = await authService.registerClientUser({
  //     name,
  //     email,
  //     password,
  //     role
  //   });

  //   res.status(201).json(newUser);
  // }

  // Register Controller
  async register(req: Request, res: Response) {
    const { name, email, password, role } = req.body;

    const result = await authService.registerClientUser({
      name,
      email: email.toLowerCase(),
      password,
      role,
    });

    res.status(201).json(result);
  }

  // OTP verification controller
  async verifyRegisterOtp(req: Request, res: Response) {
    const { email, otp } = req.body;

    const result = await authService.verifyRegisterOtp({ email, otp });
    res
      .cookie("authToken", result.token, {
        httpOnly: true,
        secure: false,
        sameSite: "lax",
        maxAge: 24 * 60 * 60 * 1000,
      })
      .status(200)
      .json({
        message: result.message,
      });
  }

  async saveProfile(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const {
      companyName,
      phoneNumber,
      website,
      timezone,
      country,
      stateProvince,
      address,
      postalCode,
    } = req.body;

    try {
      const result = await authService.saveProfileDetails(userId, {
        companyName,
        phoneNumber,
        website,
        timezone,
        country,
        stateProvince,
        address,
        postalCode,
      });
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: error });
    }
  }

  async getProfile(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    try {
      const result = await authService.getProfileDetails(userId);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ message: error || "Internal server error" });
    }
  }

  async getUserProfileById(req: Request, res: Response) {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ message: "User ID is required" });
    }

    try {
      const result = await authService.getUserProfileById(id);

      if (!result) {
        return res.status(404).json({ message: "User not found" });
      }

      res.status(200).json({
        message: "User profile fetched successfully",
        data: result,
      });
    } catch (error) {
      console.error("Error in getUserProfileById:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }

  async updateClientDetails(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    const result = await authService.updateClientDetails(userId, req.body);

    res.status(200).json(result);
  }

  // Login Controller
  // async login(req: Request, res: Response) {
  //   const { email, password } = req.body;

  //   // Validate user credentials
  //   const user = await authService.validateUserCredentials(email, password);

  //   if (!user.emailVerified) {
  //     return res.status(403).json({ message: "Please verify your email first." });
  //   }

  //   // Generate JWT Token
  //   const token = await authService.generateLoginToken(user.id, user.role);

  //   // Set the token in an HTTP-only cookie
  //   res
  //     .cookie("authToken", token, {
  //       httpOnly: true,
  //       secure: process.env.NODE_ENV === "production",
  //       sameSite: "lax",
  //       maxAge: 24 * 60 * 60 * 1000,
  //     })
  //     .status(200)
  //     .json({
  //       message: "Login successful",
  //       user,
  //       token,
  //     });
  // }
  //auth.controller.ts

  async login(req: Request, res: Response) {
    const { email, password } = req.body;

    // Validate user credentials
    const user = await authService.validateUserCredentials(email, password);

    if (!user.emailVerified) {
      return res
        .status(403)
        .json({ message: "Please verify your email first." });
    }

    // if (user.otpCode && user.otpExpiry && user.otpExpiry > new Date()) {
    //   return res.status(403).json({ message: "Please verify OTP sent to your email before login." });
    // }

    const { otp, otpExpiry } = await generateOtp();
    console.log("Generated OTP", otp);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        otpCode: otp,
        otpExpiry: otpExpiry,
      },
    });

    await this.emailService.sendEmail({
      to: user.email,
      subject: `Welcome to ${
        process.env.COMPANY_NAME || "Our Platform"
      } - Verify Your Email`,
      template: "otp-verification",
      data: {
        firstName: user.name,
        otpCode: otp,
        timeLimit: 10, // 10 minutes
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return res.status(200).json({
      status: "true",
      message: "OTP sent to your email. Please verify OTP to continue login.",
    });
  }

  async verifyLoginOtp(req: Request, res: Response) {
    const { email, otp } = req.body;

    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    if (!user.otpCode || !user.otpExpiry) {
      return res
        .status(400)
        .json({ message: "No OTP generated. Please login first." });
    }

    if (user.otpExpiry < new Date()) {
      return res
        .status(400)
        .json({ message: "OTP expired. Please login again." });
    }

    if (user.otpCode !== otp) {
      return res.status(400).json({ message: "Invalid OTP." });
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        otpCode: null,
        otpExpiry: null,
      },
    });

    const token = await authService.generateLoginToken(user.id, user.role);

    res
      .cookie("authToken", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production", // true only in production
        sameSite: "lax",
        maxAge: 24 * 60 * 60 * 1000, // 1 day
      })
      .status(200)
      .json({
        message: "Login successful",
        user,
        token,
      });
  }

  // Logout Controller
  async logout(req: Request, res: Response) {
    // Clear the cookie
    res
      .clearCookie("authToken", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      })
      .status(200)
      .json({ message: "Logout successful" });
  }

  async forgotPassword(req: Request, res: Response) {
    const { email } = req.body;

    // Send a password reset email
    const response = await authService.sendPasswordResetEmail(email);

    res
      .status(200)
      .json({ message: "Password reset email sent", data: response });
  }

  async verifyOtp(req: Request, res: Response) {
    const { email, otp } = req.body;

    // Verify the OTP
    const response = await authService.verifyOtp(email, otp);

    res
      .status(200)
      .json({ message: "OTP verified successfully", data: response });
  }

  async verifyOtpResend(req: Request, res: Response) {
    const { email } = req.body;

    // Resend the OTP
    await authService.resendOtp(email);

    res.status(200).json({ message: "OTP resent successfully" });
  }

  async resetPassword(req: Request, res: Response) {
    const { email, resetToken, newPassword } = req.body;

    // Reset the password
    await authService.resetPassword(email, resetToken, newPassword);

    res.status(200).json({ message: "Password reset successfully" });
  }

  async getMe(req: AuthenticatedRequest, res: Response) {
    const user = req.user;

    if (!user) {
      throw new AppError("User not found", 404);
    }

    console.log("User ID from token:", user);

    const userData = await authService.getUserById(user.userId);

    res.status(200).json({ user: userData, isAuthenticated: true });
  }

  async changePassword(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      console.log(req.user);

      if (!userId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const { oldPassword, newPassword, confirmPassword } = req.body;

      if (!oldPassword || !newPassword || !confirmPassword) {
        return res
          .status(400)
          .json({ error: "All password fields are required" });
      }

      const result = await authService.changePassword(
        req,
        userId,
        oldPassword,
        newPassword,
        confirmPassword
      );
      return res.status(200).json(result);
    } catch (error: any) {
      return res
        .status(400)
        .json({ error: error.message || "Something went wrong" });
    }
  }

  async sendDeleteOtp(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) return errorResponse(res, "Unauthorized");

      const result = await authService.sendDeleteOtp(userId);
      return successResponse(res, result.message);
    } catch (err: any) {
      return errorResponse(res, err.message || "Something went wrong");
    }
  }

  async deleteAccount(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      const { otp } = req.body;

      if (!userId) return errorResponse(res, "Unauthorized");
      if (!otp) return errorResponse(res, "OTP is required");

      const result = await authService.verifyOtpAndDelete(userId, otp);

      return successResponseWithData(res, result.message, result);
    } catch (error: any) {
      return errorResponse(res, error.message || "Something went wrong");
    }
  }

  async getUserAddress(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return errorResponse(res, "Unauthorized");
      }

      const result = await authService.getUserBillingAddress(userId);

      if (!result) {
        return successResponseWithData(res, "No billing address found", null);
      }

      return successResponseWithData(
        res,
        "Billing address fetched successfully",
        result
      );
    } catch (error: any) {
      console.error("Error fetching user address:", error);
      return errorResponse(
        res,
        error.message || "Failed to fetch billing address"
      );
    }
  }

  async createOrUpdateUserAddress(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return errorResponse(res, "Unauthorized");
      }

      const { country, address, state, postalCodel, street } = req.body;

      // Validate required fields
      if (!country || !address || !state || !postalCodel || !street) {
        return errorResponse(
          res,
          "All address fields are required: country, address, state, postalCodel, street"
        );
      }

      const result = await authService.createOrUpdateBillingAddress(userId, {
        country,
        address,
        state,
        postalCodel,
        street,
      });

      return successResponseWithData(
        res,
        "Billing address saved successfully",
        result
      );
    } catch (error: any) {
      console.error("Error saving user address:", error);
      return errorResponse(
        res,
        error.message || "Failed to save billing address"
      );
    }
  }

  // Admin endpoint to clean up expired OTP users
  async cleanupExpiredOtpUsers(req: Request, res: Response) {
    try {
      const result = await authService.cleanupExpiredOtpUsers();
      return successResponseWithData(res, result.message, {
        cleanedCount: result.cleanedCount,
      });
    } catch (error: any) {
      console.error("Error in cleanupExpiredOtpUsers:", error);
      return errorResponse(
        res,
        error.message || "Failed to cleanup expired OTP users"
      );
    }
  }
}
