import express, { Request, Response, NextFunction } from "express";
import dotenv from "dotenv";
import swaggerDocs from "./config/swagger.config";
import rateLimiter from "./middlewares/rate-limiter";
import { errorHandler } from "./middlewares/errorHandler";
import cookieParser from "cookie-parser";
import http from "http";
import { Server as SocketIOServer } from "socket.io";
import routers from "./routes";
import { TaskSocket } from "./ws/task-socket";
import { AppError } from "./utils/ApiError";
import helmet from "helmet";
import compression from "compression";
import client from "prom-client";
import path from "path";
import { WebhookRoute } from "./routes/webhooks/webhook.routes";
import cors from "cors";

dotenv.config();
client.collectDefaultMetrics();

class TaskManagementApp {
  app: express.Application;
  public server: http.Server;
  public io: SocketIOServer;

  constructor() {
    // Validate environment variables
    if (!process.env.PORT || !process.env.NODE_ENV || !process.env.FRONTEND_URL) {
      console.error("Error: Missing required environment variables (PORT, NODE_ENV, FRONTEND_URL)");
      process.exit(1);
    }
    console.log(`Environment: NODE_ENV=${process.env.NODE_ENV}, PORT=${process.env.PORT}, FRONTEND_URL=${process.env.FRONTEND_URL}`);

    this.app = express();
    this.server = http.createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        allowedHeaders: ["*"],
        credentials: true,
        origin: [
          "http://localhost:5173",
          "http://localhost:4173",
          "http://localhost:3000",
          "http://dataannotator.agkraft.in",
          "https://dataannotator.agkraft.in",
          "https://app.getannotator.com",
        ],
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
      },
    });

    this.configureMiddleware();
    this.configureRoutes();
    this.configureErrorHandling();
    this.setupSocketEvents();
    this.configureWrongEndpoint();
  }

  private configureMiddleware() {
    const allowedOrigins = [
      "http://localhost:5173",
      "http://localhost:4173",
      "http://localhost:3000",
      "http://dataannotator.agkraft.in",
      "https://dataannotator.agkraft.in",
      "https://app.getannotator.com",
    ];

    // Log all incoming requests
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      console.log(`Request: Method=${req.method}, URL=${req.originalUrl}, IP=${req.ip}, Origin=${req.headers.origin || "N/A"}, Host=${req.headers.host}`);
      next();
    });

    this.app.use(cookieParser());
    this.app.use(
      cors({
        origin: (origin, callback) => {
          console.log(`CORS Origin: ${origin || "N/A"}`);
          if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
          } else {
            console.warn(`CORS rejected for origin: ${origin || "N/A"}`);
            callback(null, true); // Allow undefined origins temporarily
          }
        },
        methods: ["GET", "POST", "PATCH", "DELETE", "PUT"],
        credentials: true,
      })
    );

    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(express.static(path.resolve(__dirname, "public")));
    this.app.use(helmet());
    this.app.set("trust proxy", true);
    this.app.use(compression());
    this.app.use(express.json({ limit: "10mb" }));
  }

  private setupSocketEvents(): void {
    this.io.on("connection", (socket) => {
      console.log(`Socket.IO: Client connected, ID=${socket.id}, Origin=${socket.handshake.headers.origin || "N/A"}`);
      socket.on("disconnect", () => {
        console.log(`Socket.IO: Client disconnected, ID=${socket.id}`);
      });
    });
    new TaskSocket(this.io);
  }

  public getSocketIOInstance() {
    return this.io;
  }

  private configureRoutes() {
    // Root endpoint
    this.app.get("/", (req: Request, res: Response) => {
      res.status(200).json({ message: "Welcome to Data Annotator API", version: "1.0", health: "/health", api: "/v1" });
    });

    // Health check endpoint
    this.app.get("/health", (req: Request, res: Response) => {
      res.status(200).json({ status: "OK", environment: process.env.NODE_ENV, port: process.env.PORT, frontend: process.env.FRONTEND_URL });
    });

    // Metrics endpoint
    this.app.get("/metrics", async (req: Request, res: Response) => {
      res.set("Content-Type", client.register.contentType);
      res.end(await client.register.metrics());
    });

    // API and webhook routes
    this.app.use("/webhook", new WebhookRoute().router);
    this.app.use("/v1", routers);

    // Log registered routes
    console.log("Registered Routes:");
    this.app._router.stack.forEach((middleware: any) => {
      if (middleware.route) {
        console.log(`Route: ${middleware.route.path}, Methods: ${Object.keys(middleware.route.methods).join(", ")}`);
      } else if (middleware.name === "router" && middleware.handle.stack) {
        middleware.handle.stack.forEach((handler: any) => {
          if (handler.route) {
            console.log(`Route: ${handler.route.path}, Methods: ${Object.keys(handler.route.methods).join(", ")}`);
          }
        });
      }
    });
  }

  private configureWrongEndpoint() {
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      console.error(`404 Error: Method=${req.method}, URL=${req.originalUrl}, Origin=${req.headers.origin || "N/A"}, Host=${req.headers.host}, Headers=${JSON.stringify(req.headers)}`);
      next(new AppError(`Not found: ${req.method} ${req.originalUrl}`, 404));
    });
  }

  private configureErrorHandling() {
    this.app.use(errorHandler);
  }

  public start() {
    return this.server;
  }

  public stop(): void {
    this.io.close();
    this.server.close(() => {
      console.log("Server shut down gracefully.");
    });
  }
}

export default TaskManagementApp;